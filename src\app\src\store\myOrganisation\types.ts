import { AppOrganisationModel, OrganisationModel } from 'axon-core-api-sdk/api';

export interface IMyOrganisationStore {
  isLoading: boolean;
  myApps: AppOrganisationModel[];
  organisations: OrganisationModel[];
}

export interface IMyOrganisationState extends IMyOrganisationStore {
  actions: {
    getAppsByOrganisation: (codeName: string, searchPhrase: string) => void;
    getOrganisations: () => void;
  };
}
