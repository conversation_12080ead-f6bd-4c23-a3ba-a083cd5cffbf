/*** AUTO-GENERATED FILE - EDITS WILL BE OVERWRITTEN ***/

import React, { useState, useEffect } from 'react';
import { useFormContext, useController } from 'react-hook-form';
import { filterBy } from '@progress/kendo-data-query';
import { DropDownListBlurEvent, DropDownListChangeEvent, DropDownListFilterChangeEvent } from '@progress/kendo-react-dropdowns';

import { PhlexDropdown } from 'phlex-core-ui';
import { IPhlexDropdownProps } from 'phlex-core-ui/build/src/controls/PhlexDropdown/PhlexDropdown';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexLazyMultiSelect/PhlexLazyMultiSelect';

export interface IDropdownProps extends IPhlexDropdownProps {
  fetchData?: (input?: string) => Promise<any>;
  async?: boolean;
}

export const Dropdown = ({ name, onChange, required, onBlur, fetchData, async, data, ...rest }: IDropdownProps) => {
  const [formData, setFormData] = useState<IOption[]>([]);
  const [filteredData, setFilteredData] = useState<IOption[]>([]);
  const { control, trigger } = useFormContext();

  useEffect(() => {
    if (fetchData) {
      const getData = async () => {
        const result = await fetchData();
        setFormData(result);
      };

      getData();
    }

    if (data) {
      setFormData(data);
    }
  }, [data, fetchData]);

  const {
    field: { ...inputProps },
    formState: { errors },
  } = useController({
    name,
    control,
    rules: { required },
  });

  const onChangeHandler = (e: DropDownListChangeEvent) => {
    inputProps.onChange(e.value.value);
    if (onChange) {
      onChange(e);
    }
    trigger(name);
  };

  const onBlurHandler = (e: DropDownListBlurEvent) => {
    inputProps.onBlur();
    if (onBlur) {
      onBlur(e);
    }
  };

  const handleAsyncFilterChange = async (event: DropDownListFilterChangeEvent) => {
    if (fetchData) {
      const result = await fetchData(event.filter.value);
      setFormData(result);
    }
  };

  const handleFilterChange = (event: DropDownListFilterChangeEvent) => {
    setFilteredData(filterBy(formData, event.filter));
  };

  const value = data?.find((x) => x.value === inputProps.value);

  return (
    <PhlexDropdown
      textField="label"
      dataItemKey="value"
      name={name}
      data={filteredData.length ? filteredData : formData}
      value={value}
      onChange={onChangeHandler}
      onBlur={onBlurHandler}
      required={required}
      validationMessage={errors[name]?.message?.toString()}
      filterable
      onFilterChange={async ? handleAsyncFilterChange : handleFilterChange}
      {...rest}
    />
  );
};
