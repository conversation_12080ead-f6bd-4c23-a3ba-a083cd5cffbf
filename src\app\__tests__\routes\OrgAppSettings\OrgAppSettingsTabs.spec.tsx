import React from 'react';
import { render, screen } from '../../testUtils';

import OrgAppSettingsTabs from '../../../src/routes/OrgAppSettings/tabs/OrgAppSettingsTabs';
import dummySettings from '../../testUtils/mockOrgAppSettingsGetResponse';

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

describe('OrgAppSettingsTabs', () => {
  window.ResizeObserver = ResizeObserver;

  it('should render a tab for each setting category', () => {
    // @ts-ignore
    render(<OrgAppSettingsTabs organisationAppSettings={dummySettings} />, {});

    expect(screen.getByText('Test Tab 1')).toBeInTheDocument();
    expect(screen.getByText('Test Tab 2')).toBeInTheDocument();
    expect(screen.getByText('AppSettings.DataSourcesTab')).toBeInTheDocument();
  });
});
