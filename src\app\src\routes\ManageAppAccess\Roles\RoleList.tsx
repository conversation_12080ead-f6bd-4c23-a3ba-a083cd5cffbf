import React, { FunctionComponent, ReactElement, useEffect, useState } from 'react';
import { PhlexBadge, PhlexDialog, PhlexTableSkeleton } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';
import RolesTable from 'components/Tables/ManageAppAccess/RoleTable';
import { SortDescriptor } from '@progress/kendo-data-query';
import { GridCellProps, GridPageChangeEvent, GridSortChangeEvent } from '@progress/kendo-react-grid';
import { Page } from '@progress/kendo-react-dropdowns';
import StyledComponents from './RoleList.styles';
import TableComponents from 'components/Tables/Table.styles';
import { RoleModel, RoleModelApiPagedListResult } from 'axon-core-api-sdk';
import { useRole } from 'store';
import RoleOverrideList from './RoleOverrideList';
import { roleApiService } from 'api';
import { toast } from 'react-toastify';
import { SortableTableColumn } from 'components/Tables/SortableTableColumn';
import { getFormattedPropertyName } from 'shared/utils/getFormattedPropertyName';
import { DEFAULT_PAGE_SIZE } from 'constants/dataScreen';

const RoleList: FunctionComponent<{
  organisationCodeName: string;
  appCodeName: string;
  filter: string | null;
  searchPhrase: string | null;
}> = ({ organisationCodeName, appCodeName, filter, searchPhrase }) => {
  const { t } = useTranslation();
  const { TableRowDesc, TableRowHeader } = StyledComponents;
  const [roleDetailDialogOpen, setRoleDetailDialogOpen] = useState<boolean>(false);
  const initialSort: Array<SortDescriptor> = [{ field: 'roleName', dir: 'asc' }];
  const [sort, setSort] = useState(initialSort);
  const [pagination, setPagination] = useState<Page>({ take: DEFAULT_PAGE_SIZE, skip: 0 });
  const [confirmationDialogVisible, setConfirmationDialogVisible] = useState(false);
  const [deleteRole, setDeleteRole] = useState<GridCellProps>();
  const [allRoles, setAllRoles] = useState<RoleModelApiPagedListResult>();
  const { TableIcon } = TableComponents;

  const onSortChange = (e: GridSortChangeEvent) => {
    setPagination({ take: DEFAULT_PAGE_SIZE, skip: 0 });
    setSort(e.sort?.filter((sortFilter: SortDescriptor) => sortFilter.field !== ''));
  };

  const onPageChange = (event: GridPageChangeEvent) => {
    setPagination(event.page);
  };

  const {
    isLoadingRoles,
    roles,
    selectedRole,
    isSelectedRoleLoading,
    actions: { getFiltered, getByRoleId, getRoleDefinitionByOrgAndAppCode, getScopesAndResourcesForOrganisation },
  } = useRole();

  useEffect(() => {
    setAllRoles(roles);
  }, [roles]);

  let { isRoleDefintionLoading, roleDefinition } = useRole();

  useEffect(() => {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    isRoleDefintionLoading = null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
    roleDefinition = [];
  }, []);

  useEffect(() => {
    if (filter) {
      setSort(initialSort);
      setPagination({ take: DEFAULT_PAGE_SIZE, skip: 0 });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter]);

  useEffect(() => {
    if (organisationCodeName) {
      const regex = new RegExp(t('Forms.SafeCharactersRegex'));
      if (searchPhrase && !regex.test(searchPhrase)) {
        setAllRoles(undefined);
        return;
      }

      getRoleDefinitionByOrgAndAppCode(organisationCodeName, appCodeName);
      getFiltered(
        organisationCodeName,
        appCodeName,
        pagination.take,
        pagination.skip,
        sort.length === 0 ? 'roleName desc' : `${sort[0].field} ${sort[0].dir}`,
        filter ?? ''
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organisationCodeName, appCodeName, sort, pagination, filter, searchPhrase]);

  useEffect(() => {
    const isAnyRolePermissionHasScopes = roleDefinition?.filter((role) => role.hasScopes);

    if (isAnyRolePermissionHasScopes) {
      getScopesAndResourcesForOrganisation(appCodeName, organisationCodeName);
    }
    if (roleDefinition.length === 0 && isRoleDefintionLoading === false) {
      toast.error(t('RoleList.NoRoleDefinition'));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [roleDefinition, isRoleDefintionLoading]);

  if (isLoadingRoles === null) return null;
  if (isLoadingRoles) return <PhlexTableSkeleton />;

  const itemTextCell = (cell: GridCellProps, dataItemProp: string): ReactElement => {
    const text = cell.dataItem[dataItemProp];

    //
    const desc = cell.dataItem['permissions']
      ?.filter((p: any) => p.allow === true)
      .map((d: any) => {
        return getFormattedPropertyName(d.name);
      });
    //
    return (
      <td title={desc} onClick={() => roleDetail(cell.dataItem['id'])}>
        <TableRowHeader>{text}</TableRowHeader>
        <TableRowDesc>{desc.join(', ')}</TableRowDesc>
      </td>
    );
  };

  const rowClick = (cell: GridCellProps, dataItemProp: string): ReactElement => {
    const text = cell.dataItem[dataItemProp];
    return (
      <td title={text} onClick={() => roleDetail(cell.dataItem['id'])}>
        {text}
      </td>
    );
  };

  const roleDetail = (id: string) => {
    getByRoleId(id, organisationCodeName, appCodeName);
    setRoleDetailDialogOpen(true);
  };

  const deleteCell = (gridCell: GridCellProps) => {
    if (gridCell.dataItem['roleType']?.toLowerCase() !== 'system') {
      return (
        <TableIcon onClick={() => deleteRolePopup(gridCell)} title={t('RoleList.DeleteRole')}>
          delete
        </TableIcon>
      );
    } else {
      return <td></td>;
    }
  };

  const deleteRolePopup = (gridCell: GridCellProps) => {
    setConfirmationDialogVisible(true);
    setDeleteRole(gridCell);
  };

  const deleteRoleRequest = () => {
    roleApiService
      .deleteRole(deleteRole?.dataItem['id'], organisationCodeName, appCodeName)
      .then(() => {
        setConfirmationDialogVisible(false);
        getFiltered(organisationCodeName, appCodeName, 20, 0, 'roleName asc', filter ?? '');
        toast.success(t('RoleList.DeleteRoleSuccess'));
      })
      .catch(() => {
        toast.error(t('Shared.ApiFailedErrorMessage'));
      });
  };

  const columns: SortableTableColumn[] = [
    {
      identifier: 'roleName',
      title: t('RoleList.Name'),
      cell: (cell: GridCellProps) => itemTextCell(cell, 'roleName'),
      sortable: true,
    },
    {
      identifier: 'roleType',
      title: t('RoleList.Type'),
      cell: (cell: GridCellProps) => rowClick(cell, 'roleType'),
      sortable: true,
      width: '120px',
    },
    {
      identifier: 'isEnabled',
      title: t('RoleList.Status'),
      cell: (cell: GridCellProps) => rowClick(cell, 'isEnabled'),
      sortable: true,
      width: '120px',
    },
    { identifier: '', title: '', cell: deleteCell, width: '40px' },
  ];

  const statusColorMap = new Map();
  statusColorMap.set(true, 'green');
  statusColorMap.set(false, 'red');

  const statusMap = new Map();
  statusMap.set(true, t('RoleList.Active'));
  statusMap.set(false, t('RoleList.Inactive'));

  return (
    <div data-testid="role-list-wrapper">
      <RolesTable
        isLoading={false}
        data={
          allRoles?.data?.map((role: RoleModel) => ({
            ...role,
            isEnabled: (
              <PhlexBadge
                statusColor={statusColorMap.get(role.isEnabled)}
                label={statusMap.get(role.isEnabled)}
                tooltip={statusMap.get(role.isEnabled)}
                size="small"
              />
            ),
          })) ?? []
        }
        uniqueIdField={'id'}
        sortable={true}
        pageable={true}
        columns={columns}
        onSortChange={onSortChange}
        onPageChange={onPageChange}
        sort={sort}
        total={allRoles?.paging?.totalItemCount}
        take={pagination.take}
        skip={pagination.skip}
        nameOfRows={t('RoleList.Roles')}
        noRecordsMessage={t('Forms.Placeholders.NoRolesFound')}
      />
      {selectedRole && (
        <RoleOverrideList
          selectedRole={selectedRole}
          roleDetailDialogOpen={roleDetailDialogOpen}
          isSelectedRoleLoading={isSelectedRoleLoading}
          organisationCodeName={organisationCodeName}
          appCodeName={appCodeName}
          filter={filter}
          onRoleDialogClose={() => setRoleDetailDialogOpen(false)}
        ></RoleOverrideList>
      )}

      <PhlexDialog
        confirmType="destructive"
        closeLabel={t('RoleList.CancelButton')}
        confirmLabel={t('RoleList.DeleteRoleButton')}
        isOpen={confirmationDialogVisible}
        heading={t('RoleList.DeleteRole')}
        message={t('RoleList.DeleteDescription', { roleName: deleteRole?.dataItem['roleName'] })}
        onClose={() => setConfirmationDialogVisible(false)}
        onConfirm={deleteRoleRequest}
      />
    </div>
  );
};

export default RoleList;
