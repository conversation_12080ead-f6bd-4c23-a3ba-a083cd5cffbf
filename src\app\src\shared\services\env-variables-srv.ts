type EnvironmentVariable =
  | 'APP_VERSION'
  | 'AXON_CORE_UI_PUBLIC_URL'
  | 'AXON_CORE_API_BASE_URL'
  | 'AXON_CHAT_API_BASE_URL'
  | 'NODE_ENV'
  | 'AXON_CORE_TIMEOUT_MAX_ATTEMPTS' //The maximum number of times the a timed out call will be retried
  | 'AXON_CORE_TIMEOUT_MIN_DELAY' //The minimum amount of time to wait after a timeout
  | 'AXON_CORE_TIMEOUT_MAX_DELAY' //The maximum amount of time to wait after a timeout
  | 'AXON_CORE_TIMEOUT_CANCELLATION_TIME' //How long to wait before cancelling a request
  | 'AXON_SHARED_LOCAL_TOKEN' //forces the usage of token rather than cookie auth. This should be removed when we have a solution for running locally when targeting remote api
  | 'AXON_SHARED_LOCAL_COOKIE' //Opt in to use cookie auth locally
  | 'AXON_SHARED_LOCAL_USER'; //User details to populate axon_ActiveAccount with when using AXON_SHARED_LOCAL_TOKEN

export default {
  getVariable: (variable: EnvironmentVariable): string => {
    if (!(window as any)._env_) return variable;
    return (window as any)._env_[variable];
  },
};
