import Axios from 'axios';
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { auditApiService } from 'api';
import { IAuditsStore, IAuditParams, AuditEvents } from './types';
import { DataItem } from 'phlex-core-ui';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AXON_CORE } from 'constants/app';

const name = 'audit';
const initialFilters = {
  eventTypeValue: [],
  eventCategoryValue: [],
  eventOutcomeValue: [],
  ipAddressValue: '',
  correlationIdValue: '',
  userValue: '',
  fromDateValue: null,
  toDateValue: null,
};

const initialState: IAuditsStore = {
  isLoadingAudits: true,
  auditLogs: [],
  error: false,
  filterString: '',
  sort: [{ field: 'StartDate', dir: 'desc' }],
  filters: initialFilters,
} as IAuditsStore;

export const getAuditsAsync = createAsyncThunk<
  DataItem[],
  { take: number; skip: number; orderBy: string; filters: string; auditUrl: string }
>(`organisation/appCodeName/getAuditsAsync`, async ({ take, skip, orderBy, filters, auditUrl }) => {
  const params: IAuditParams = { $top: take, $skip: skip };
  if (orderBy) {
    params.$orderby = orderBy;
  }
  if (filters) {
    params.$filter = filters;
  }

  const response = await Axios.get<any>(auditUrl, {
    params: params,
  });
  return response?.data?.value ?? ([] as DataItem[]);
});

export const getAuditFiltersAsync = createAsyncThunk<AuditEvents, { organisationName: string; appCodeName: string; filterUrl?: string }>(
  `organisation/appCodeName/getAuditFiltersAsync`,
  async ({ organisationName, appCodeName, filterUrl }) => {
    if (appCodeName === AXON_CORE) {
      const response: any = await auditApiService.getAuditFilters(organisationName, appCodeName);
      return response?.data?.data || ({} as AuditEvents);
    } else if (filterUrl) {
      const response = await Axios.get<AuditEvents>(filterUrl);
      return response?.data || ({} as AuditEvents);
    }
  }
);

export const getAuditFiltersUrlAsync = createAsyncThunk<string, { organisationName: string; appCodeName: string }>(
  `organisation/appCodeName/getAuditFiltersUrlAsync`,
  async ({ organisationName, appCodeName }) => {
    const response: any = await auditApiService.getAuditFiltersUrl(organisationName, appCodeName);
    return response?.data?.data ?? ({} as string);
  }
);

export const getAuditsUrlAsync = createAsyncThunk<string, { organisationName: string; appCodeName: string }>(
  `organisation/appCodeName/getAuditsUrlAsync`,
  async ({ organisationName, appCodeName }) => {
    const response: any = await auditApiService.getAuditsUrl(organisationName, appCodeName);
    return response?.data?.data ?? ({} as string);
  }
);

const slice = createSlice({
  name,
  initialState,
  reducers: {
    resetAuditStore(state: IAuditsStore) {
      state.auditLogs = [];
      state.isLoadingAudits = true;
    },
    setEventTypeValue(state: IAuditsStore, action: PayloadAction<IOption[]>) {
      state.filters.eventTypeValue = action.payload;
    },
    setEventCategoryValue(state: IAuditsStore, action: PayloadAction<IOption[]>) {
      state.filters.eventCategoryValue = action.payload;
    },
    setEventOutcomeValue(state: IAuditsStore, action: PayloadAction<IOption[]>) {
      state.filters.eventOutcomeValue = action.payload;
    },
    setIPAddressValue(state: IAuditsStore, action: PayloadAction<string>) {
      state.filters.ipAddressValue = action.payload;
    },
    setCorrelationIdValue(state: IAuditsStore, action: PayloadAction<string>) {
      state.filters.correlationIdValue = action.payload;
    },
    setUserValue(state: IAuditsStore, action: PayloadAction<string>) {
      state.filters.userValue = action.payload;
    },
    setFromDateValue(state: IAuditsStore, action: PayloadAction<Date | null>) {
      state.filters.fromDateValue = action.payload;
    },
    setToDateValue(state: IAuditsStore, action: PayloadAction<Date | null>) {
      state.filters.toDateValue = action.payload;
    },
    setFilterString(state: IAuditsStore, action: PayloadAction<string>) {
      state.filterString = action.payload;
    },
    setSort(state: IAuditsStore, action: PayloadAction<Array<SortDescriptor>>) {
      state.sort = action.payload;
    },
    clearFilters(state: IAuditsStore) {
      state.filters = initialFilters;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getAuditsAsync.pending, (state) => {
      state.isLoadingAudits = true;
      state.error = false;
    });
    builder.addCase(getAuditsAsync.fulfilled, (state, action: PayloadAction<DataItem[]>) => {
      state.isLoadingAudits = false;
      state.auditLogs = action.payload;
      state.error = false;
    });
    builder.addCase(getAuditsAsync.rejected, (state) => {
      state.isLoadingAudits = false;
      state.error = true;
    });
    builder.addCase(getAuditFiltersAsync.pending, (state) => {
      state.error = false;
    });
    builder.addCase(getAuditFiltersAsync.fulfilled, (state, action: PayloadAction<AuditEvents>) => {
      state.auditEvents = action.payload;
      state.error = false;
    });
    builder.addCase(getAuditFiltersAsync.rejected, (state) => {
      state.error = true;
    });
    builder.addCase(getAuditFiltersUrlAsync.pending, (state) => {
      state.error = false;
    });
    builder.addCase(getAuditFiltersUrlAsync.fulfilled, (state, action: PayloadAction<string>) => {
      state.filterUrl = action.payload;
      state.error = false;
    });
    builder.addCase(getAuditFiltersUrlAsync.rejected, (state) => {
      state.error = true;
    });
    builder.addCase(getAuditsUrlAsync.pending, (state) => {
      state.error = false;
    });
    builder.addCase(getAuditsUrlAsync.fulfilled, (state, action: PayloadAction<string>) => {
      state.auditUrl = action.payload;
      state.error = false;
    });
    builder.addCase(getAuditsUrlAsync.rejected, (state) => {
      state.error = true;
    });
  },
});
export const {
  resetAuditStore,
  setEventTypeValue,
  setEventCategoryValue,
  setEventOutcomeValue,
  setIPAddressValue,
  setCorrelationIdValue,
  setUserValue,
  setFromDateValue,
  setToDateValue,
  setFilterString,
  setSort,
  clearFilters,
} = slice.actions;
export default slice.reducer;
