/*** AUTO-GENERATED FILE - EDITS WILL BE OVERWRITTEN ***/

import React, { useCallback, useState, useRef } from 'react';
import { useFormContext, useController } from 'react-hook-form';
import { MultiSelectChangeEvent } from '@progress/kendo-react-dropdowns';

import { PhlexMultiSelect } from 'phlex-core-ui';
import { IPhlexMultiSelectProps } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexLazyMultiSelect/PhlexLazyMultiSelect';
import { DEBOUNCE_TIMEOUT } from '../../constants/dataScreen';

export const Multiselect = ({ name, required, fetchData, ...rest }: IPhlexMultiSelectProps) => {
  const [filterValue, setFilterValue] = useState<string>('');
  const timeoutRef = useRef<number | null>(null);
  const { control, trigger } = useFormContext();

  const {
    field: { ...inputProps },
    formState: { errors },
  } = useController({
    name,
    control,
    rules: { required },
  });

  const fetchDataCallback = useCallback<() => Promise<any>>(async () => {
    const response = await fetchData(filterValue);
    return response;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterValue]);

  const handleFilterChange = (e: any) => {
    if (timeoutRef.current !== null) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = window.setTimeout(() => {
      setFilterValue(e.filter.value);
    }, DEBOUNCE_TIMEOUT);
  };

  const onChangeHandler = (x: IOption[], e: MultiSelectChangeEvent) => {
    inputProps.onChange(e.value);
    trigger(name);
  };

  const onBlurHandler = () => {
    inputProps.onBlur();
  };

  return (
    <PhlexMultiSelect
      name={name}
      onChange={onChangeHandler}
      onBlur={onBlurHandler}
      required={required}
      validationMessage={errors[name]?.message?.toString()}
      fetchData={fetchDataCallback}
      onFilterChange={handleFilterChange}
      {...rest}
    />
  );
};
