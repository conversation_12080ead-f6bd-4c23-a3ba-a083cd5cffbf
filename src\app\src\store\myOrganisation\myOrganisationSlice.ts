import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { appApiService, organisationApiService } from 'api';
import { IMyOrganisationStore } from './types';
import { AppOrganisationModel, OrganisationModel } from 'axon-core-api-sdk';

const name = 'myOrganisation';
const emptyArrayOfApps = [] as AppOrganisationModel[];

const initialState: IMyOrganisationStore = {
  isLoading: false,
  myApps: [] as AppOrganisationModel[],
  organisations: [] as OrganisationModel[],
};
export const getAppsByOrganisationAsync = createAsyncThunk<AppOrganisationModel[], { organisationId: string; searchPhrase: string }>(
  `${name}/getAppsByOrganisation`,
  async ({ organisationId, searchPhrase }) => {
    const filter = `isDeleted equal false and displayName.startsWith("${searchPhrase}", StringComparison.InvariantCultureIgnoreCase)`;
    const response = await appApiService.getAppsByOrganisation(organisationId, filter, 'displayName');
    return !response?.data?.data
      ? emptyArrayOfApps
      : response.data.data
          .filter((x) => x.isEnabled)
          .sort((a, b) => a.displayName.toLowerCase().localeCompare(b.displayName.toLowerCase()));
  }
);

export const getOrganisationsAsync = createAsyncThunk<OrganisationModel[]>(`organisations/getOrganisationsAsync`, async () => {
  const filter = `isDeleted equal false`;
  const response = await organisationApiService.getOrganisationList(filter, 'displayName', 0, 100);
  if (!response?.data?.data) return [] as OrganisationModel[];
  return response.data.data;
});
const slice = createSlice({
  name: name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getAppsByOrganisationAsync.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(getAppsByOrganisationAsync.fulfilled, (state, action: PayloadAction<AppOrganisationModel[]>) => {
      state.isLoading = false;
      state.myApps = action.payload;
    });
    builder.addCase(getAppsByOrganisationAsync.rejected, (state) => {
      state.isLoading = false;
    });
    builder.addCase(getOrganisationsAsync.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(getOrganisationsAsync.fulfilled, (state, action: PayloadAction<OrganisationModel[]>) => {
      state.isLoading = false;
      state.organisations = action.payload;
    });
    builder.addCase(getOrganisationsAsync.rejected, (state) => {
      state.isLoading = false;
    });
  },
});
export default slice.reducer;
