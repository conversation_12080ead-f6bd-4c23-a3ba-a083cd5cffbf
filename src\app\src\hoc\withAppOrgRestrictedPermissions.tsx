import React from 'react';

import { useAuthStore } from 'store';
import { authService } from 'shared/services';
import { Navigate, useLocation, useParams } from 'react-router-dom';
import { routes, APP_ACTIONS_NESTED_ROUTES } from 'routes';
import { AXON_CORE } from 'constants/app';

type AccessType = 'all' | 'any';

type WithAppOrgRestrictedPermissionsProps = {
  permissions: string[];
  accessType: AccessType;
  redirect: boolean;
};

function withAppOrgRestrictedPermissions<T>(
  WrappedComponent: React.ComponentType<T>,
  permissions: string[],
  accessType: AccessType = 'all',
  redirect = false
) {
  const displayName = WrappedComponent.displayName ?? WrappedComponent.name ?? 'Component';

  const ComponentWithRestrictedPermissions = (props: Omit<T, keyof WithAppOrgRestrictedPermissionsProps>) => {
    const params = useParams<{ codeName: string; appCodeName: string }>();
    const { pathname } = useLocation();
    const { actions, organisationAppPermissions, isLoading } = useAuthStore();

    let appCode = params.appCodeName ? params.appCodeName : AXON_CORE;

    //If org code name and app code name aren't in the url then 404 in the same way as a permission failure
    if (!params.codeName) {
      return redirect ? <Navigate to={routes.notFound} /> : null;
    }

    if (
      pathname?.includes(`${params.codeName}/manage-app-access`) ||
      pathname?.includes(`${params.codeName}/${params.appCodeName}/settings`) ||
      pathname?.includes(`${params.codeName}/${params.appCodeName}/app-actions/${APP_ACTIONS_NESTED_ROUTES.APP_SETTINGS}`) ||
      pathname?.includes(`${params.codeName}/${params.appCodeName}/app-actions/${APP_ACTIONS_NESTED_ROUTES.MANAGE_ACCESS}`)
    ) {
      appCode = AXON_CORE;
    }

    if (isLoading) {
      return null;
    }

    //Try to find permissions for app/org combo
    const foundPermissions = organisationAppPermissions.find((x) => x.appCodeName === appCode && x.orgCodeName === params.codeName);

    //If they can't be found then trigger the request of them
    if (foundPermissions === undefined) {
      actions.getAppOrgPermissions(appCode, params.codeName);
      return null;
    }

    if (!authService.hasAccess(foundPermissions?.permissions, permissions, accessType)) {
      return redirect ? <Navigate to={routes.notFound} /> : null;
    }

    return <WrappedComponent {...(props as any)} />;
  };

  ComponentWithRestrictedPermissions.displayName = `withTheme(${displayName})`;

  return ComponentWithRestrictedPermissions;
}

export default withAppOrgRestrictedPermissions;
