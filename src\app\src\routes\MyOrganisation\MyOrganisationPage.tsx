import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { InputChangeEvent } from '@progress/kendo-react-inputs';

import { PhlexLayout, PhlexSearchBox, PhlexCardSkeleton } from 'phlex-core-ui';

import { useGlobalStore, useMyOrganisationStore } from 'store';
import { withOrganisation } from 'hoc';
import { OrganisationModel } from 'axon-core-api-sdk/api';
import StyledComponents from './AppsGrid.styles';
import AppsGrid from './AppsGrid';
import { useNavigate } from 'react-router-dom';
import { routes } from 'routes/routes';
import { AXON_CORE } from 'constants/app';
import { AppOrganisationModel } from 'axon-core-api-sdk';

const MyOrganisationPage: FC<{ organisation: OrganisationModel }> = ({ organisation }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { NoResults, PageActions, PageTopBar } = PhlexLayout;
  const { Main, MyAppsHeading, PopoutMenu } = StyledComponents;

  const [searchPhrase, setSearchPhrase] = useState<string>('');
  const [myFilteredApps, setMyFilteredApps] = useState<AppOrganisationModel[]>([]);

  const {
    homeOrganisation,
    selectedOrganisation,
    actions: { setSelectedOrganisation, setSelectedHeaderLogo, setSelectedStyleSheetUrl, setSelectedAvatar },
  } = useGlobalStore();

  const {
    isLoading,
    myApps,
    actions: { getAppsByOrganisation },
  } = useMyOrganisationStore();

  const menuOptions: {
    label: string;
    icon: string;
    onClick: () => void;
  }[] = [];

  if (organisation.showAudits) {
    menuOptions.push({
      label: t('OrganisationPage.PlatformAuditLogLabel'),
      icon: 'Article',
      onClick: () => navigate(routes.orgAuditLog(organisation.codeName, AXON_CORE)),
    });
  }

  useEffect(() => {
    const apps = myApps.filter((apps) => !apps.isSystemApp) ?? [];
    setMyFilteredApps(apps);
  }, [myApps]);

  useEffect(() => {
    if (selectedOrganisation?.codeName === organisation.codeName) return;
    setSelectedOrganisation(organisation.codeName);
    setSelectedHeaderLogo(organisation.theme?.headerUrl ?? '');
    setSelectedAvatar(organisation.theme?.avatarUrl ?? '');
    setSelectedStyleSheetUrl(organisation?.theme?.stylesheetUrl ?? '');

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organisation, homeOrganisation]);

  useEffect(() => {
    if (selectedOrganisation === undefined || !selectedOrganisation.id) return;
    if (!searchPhrase) {
      getAppsByOrganisation(organisation.id, searchPhrase);
    } else {
      const timeoutId = setTimeout(() => getAppsByOrganisation(organisation.id, searchPhrase), 500);
      return () => clearTimeout(timeoutId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchPhrase, selectedOrganisation]);

  const renderApps = () => {
    if (isLoading) return <PhlexCardSkeleton greyBackground />;
    if (myFilteredApps && myFilteredApps.length > 0 && selectedOrganisation !== undefined)
      return <AppsGrid apps={myFilteredApps} organisationSubPath={selectedOrganisation.codeName} />;
    return <NoResults>{t('Shared.NoMatches', { type: t('AppsAdmin.ResultsType') })}</NoResults>;
  };

  return (
    <Main>
      <PageTopBar>
        <MyAppsHeading data-test-id="phlex-breadcrumbs">{t('Breadcrumbs.MyApps')}</MyAppsHeading>
        <PageActions>
          <PhlexSearchBox
            inputValue={searchPhrase}
            inputPlaceholder={t('Shared.SearchApps')}
            onInputChange={(e: InputChangeEvent) => setSearchPhrase(e.target.value as string)}
            clearSearch
            onClearSearch={() => setSearchPhrase('')}
          />
          {menuOptions.length > 0 && <PopoutMenu actions={menuOptions} />}
        </PageActions>
      </PageTopBar>
      {renderApps()}
    </Main>
  );
};

export default withOrganisation(MyOrganisationPage);
