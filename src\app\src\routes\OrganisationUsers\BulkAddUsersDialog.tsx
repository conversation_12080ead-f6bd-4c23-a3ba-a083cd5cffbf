import React, { ReactNode, useEffect, useState } from 'react';
import <PERSON>, { UnparseObject } from 'papaparse';
import { t } from 'i18next';
import { UploadFileInfo } from '@progress/kendo-react-upload';
import { GroupModel, OrganisationGroupModel } from 'axon-core-api-sdk';
import { PhlexDialog, PhlexIcon, PhlexAnimation, PhlexButton } from 'phlex-core-ui';

import BulkAddComponents from './BulkAddUsersDialog.styles';
import AxonUploader from 'components/Upload/AxonUploader';
import organisationApi from 'api/organisationApi';
import { ICsvResultProps, IUserDialogProps, IUserErrorProps, IUserGroups, IUserProps } from './BulkAddUsersDialog.types';
import { organisationApiService } from 'api';
import { USER_STATUSES } from 'constants/users';
import { cloneDeep } from 'lodash';
import { environmentVariableSrv } from 'shared/services';

const BulkAddUsersDialog = (props: IUserDialogProps): ReactNode => {
  const { isOpen, orgCodeName, onClose } = props;
  const {
    DialogWrapper,
    TextContainer,
    InfoText,
    InfoPanel,
    DownloadText,
    ImportSummary,
    ImportInfo,
    ProgressWrapper,
    ProgressMsg,
    CompleteText,
  } = BulkAddComponents;
  const { UserImport } = PhlexAnimation;

  const [isImporting, setIsImporting] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [userData, setUserData] = useState<IUserProps[]>([]);
  const [errorUserData, setErrorUserData] = useState<unknown[]>([]);
  const [progressCount, setProgressCount] = useState(0);
  const [failedCount, setFailedCount] = useState(0);
  const [allGroups, setAllGroups] = useState<{ label: string; value: string }[]>([]);
  const [fileUploaded, setFileUploaded] = useState(false);
  const [invalidValidationMessage, setInvalidValidationMessage] = useState('');
  const [totalCount, setTotalCount] = useState(0);
  const [allNewGroups, setAllNewGroups] = useState<{ label: string; value: string; error?: string }[]>([]);

  const headerKeys = {
    Email: 'Email Address',
    Name: 'Name',
    Groups: 'Group(s)',
    Reason: 'Reason',
  };

  useEffect(() => {
    const groupPageLimit = 1000;

    const getAllGroups = async () => {
      await organisationApiService.getOrganisationGroups(orgCodeName, '', '', 0, groupPageLimit).then((response) => {
        setAllGroups(addAllGroups(response?.data?.data?.data) ?? []);
      });
    };

    const addAllGroups = (data: OrganisationGroupModel[] | null | undefined) => {
      return data?.map((group: GroupModel) => ({ label: group.name, value: group.id }));
    };

    getAllGroups();
  }, [orgCodeName]);

  useEffect(() => {
    if (totalCount === progressCount + failedCount && (userData?.length > 0 || errorUserData?.length > 0)) {
      setIsImporting(false);
      setIsError(failedCount > 0);
      setIsSuccess(progressCount === totalCount);
    }
  }, [progressCount, failedCount, totalCount, userData, errorUserData]);

  useEffect(() => {
    if (allNewGroups?.length > 0) {
      const allUsers = userData;

      const uploadUsers = async () => {
        for (const user of allUsers) {
          const modifiedUser = getModifiedUserGroup(user, allNewGroups);
          if (modifiedUser.error) {
            prepareErrorData(modifiedUser, modifiedUser.error);
          } else {
            await onConfirmAddUser({
              emailAddress: modifiedUser.emailAddress,
              fullName: modifiedUser.fullName,
              groups: modifiedUser.groups,
            });
            await delay(50, 100);
          }
        }
      };

      uploadUsers();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allNewGroups, userData]);

  const getModifiedUserGroup = (user: IUserProps, newGroups: { label: string; value: string; error?: string }[]) => {
    const modifiedUser = cloneDeep(user);
    user.newGroups?.forEach((ng) => {
      const groupIndex = newGroups?.findIndex((group) => group.label?.toLowerCase()?.trim() === ng?.toLowerCase()?.trim());
      if (groupIndex > -1) {
        modifiedUser.error = newGroups[groupIndex].error;
        modifiedUser.groups?.push(newGroups[groupIndex].value);
        modifiedUser.newGroups?.splice(groupIndex, 1);
      }
    });

    return modifiedUser;
  };

  const getPrimaryLabel = (): string => {
    if (isImporting || isSuccess) {
      return t('Buttons.Finish');
    } else if (!isImporting && isError) {
      return t('OrganisationUsers.BulkAddPopup.ReUploadLabel');
    }
    return t('Buttons.Import');
  };

  const onPrimaryClick = () => {
    if (isSuccess) {
      closeAndReset();
    } else if (!isImporting && isError) {
      setIsError(false);
      resetFileStatus();
    } else {
      setIsImporting(true);
      startUserImport();
    }
  };

  const startUserImport = async () => {
    let allNewGroups = [...new Set(userData.map((user) => user.newGroups)?.flat())]?.filter((group) => group) as string[];
    allNewGroups = removeDuplicatesForCaseSensitive(allNewGroups);
    if (allNewGroups?.length > 0) {
      await resolveGroupApiPromises(allNewGroups);
    } else {
      for (const user of userData) {
        await onConfirmAddUser({
          emailAddress: user.emailAddress,
          fullName: user.fullName,
          groups: user.groups,
        });
        await delay(50, 100);
      }
    }
  };

  const removeDuplicatesForCaseSensitive = (allNewGroups: string[]) => {
    const uniqueArray: string[] = [];
    const seen = new Set();

    allNewGroups?.forEach((group) => {
      if (!seen.has(group.toLowerCase())) {
        uniqueArray.push(group);
        seen.add(group.toLowerCase());
      }
    });

    return uniqueArray;
  };

  const resolveGroupApiPromises = async (newGroups: string[]) => {
    const allNewGroups = await addGroups(newGroups);
    if (allNewGroups && allNewGroups?.length > 0) {
      setAllGroups((prev) => {
        const allGroups = [...prev, ...allNewGroups];
        return allGroups;
      });
      setAllNewGroups(allNewGroups);
    }
  };

  const addGroups = async (newGroups: string[]) => {
    const responses: { label: string; value: string; error?: string }[] = [];

    for (const newGroup of newGroups) {
      await organisationApi
        .AddGroup(orgCodeName, newGroup)
        .then((response) => {
          responses.push({
            label: response.data.data?.name ?? '',
            value: response.data.data?.id ?? '',
          });
        })
        .catch((error) => {
          if (error.response?.status === 400 && error.response?.data?.errors?.Name?.length > 0) {
            responses.push({
              label: JSON.parse(error.response?.config?.data)?.name,
              value: '',
              error: t('OrganisationGroups.AddGroupPopup.Toasts.InvalidGroupName'),
            });
          } else if (error.response.status === 409) {
            responses.push({
              label: JSON.parse(error.response?.config?.data)?.name,
              value: '',
              error: t('OrganisationGroups.AddGroupPopup.Toasts.GroupAlreadyExists'),
            });
          } else {
            responses.push({
              label: JSON.parse(error.response?.config?.data)?.name,
              value: '',
              error: t('Shared.ApiFailedErrorMessage'),
            });
          }
        });

      await delay(50, 100);
    }

    return responses;
  };

  //Delay by a random time period between the provided min and max
  function delay(minDelay: number, maxDelay: number): Promise<unknown> {
    return new Promise((resolve) => setTimeout(resolve, Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay));
  }

  const onConfirmAddUser = async (data: IUserProps) => {
    setIsImporting(true);
    const body = {
      name: data.fullName,
      email: data.emailAddress,
      status: USER_STATUSES.ACTIVE,
      groups: data.groups ?? [],
      childOrganisations: [],
    };
    const retrySettings = getTimeoutRetrySettings();
    let attempts = 0;
    let inRetryState = false;
    let response = null;
    do {
      if (inRetryState) {
        await delay(retrySettings.minimumDelay, retrySettings.maximumDelay);
      }
      attempts = attempts += 1;
      response = await organisationApiService
        .addUsersToOrganisation(orgCodeName, body, { signal: AbortSignal.timeout(retrySettings.cancellationTimeout) })
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            setProgressCount((prev) => prev + 1);
          }
          inRetryState = false;
        })
        .catch((error) => {
          if (error.name === 'TimeoutError' || error.name === 'AbortError' || error.name == 'CanceledError') {
            if (attempts > retrySettings.maxAttempts) {
              inRetryState = false;
              prepareErrorData(data, t('Shared.ApiTimedOutMessage'));
              return;
            } else {
              inRetryState = true;
              return;
            }
          }
          inRetryState = false;
          if (error.response.status === 400 && error.response.data.errors.Email?.length > 0) {
            prepareErrorData(data, t('OrganisationUsers.AddUserPopup.EmailInvalid'));
          } else if (error.response.status === 400 && error.response.data.errors.Name?.length > 0) {
            prepareErrorData(data, t('OrganisationUsers.AddUserPopup.InvalidName'));
          } else if (error.response.status === 409 && error.response.data.type === 'AccessEntity') {
            prepareErrorData(data, t('OrganisationUsers.AddUserPopup.UserAlreadyExists'));
          } else {
            prepareErrorData(data, t('Shared.ApiFailedErrorMessage'));
          }
        });
    } while (inRetryState);

    return response;
  };

  const getTimeoutRetrySettings = () => {
    const maxAttemptsVariable = environmentVariableSrv.getVariable('AXON_CORE_TIMEOUT_MAX_ATTEMPTS');
    const retryMinDelayVariable = environmentVariableSrv.getVariable('AXON_CORE_TIMEOUT_MIN_DELAY');
    const retryMaxDelayVariable = environmentVariableSrv.getVariable('AXON_CORE_TIMEOUT_MAX_DELAY');
    const cancellationTimeoutVariable = environmentVariableSrv.getVariable('AXON_CORE_TIMEOUT_CANCELLATION_TIME');

    return {
      maxAttempts: maxAttemptsVariable ? Number(maxAttemptsVariable) : 3,
      minimumDelay: retryMinDelayVariable ? Number(retryMinDelayVariable) : 1000,
      maximumDelay: retryMaxDelayVariable ? Number(retryMaxDelayVariable) : 3000,
      cancellationTimeout: cancellationTimeoutVariable ? Number(cancellationTimeoutVariable) : 5000,
    };
  };

  const prepareErrorData = (data: IUserProps, reason: string) => {
    updateErrorData({
      fullName: data.fullName,
      emailAddress: data.emailAddress,
      groups: data.groupNames,
      reason,
    });
  };

  const getSecondaryLabel = (): string => {
    if (!isImporting && isError) {
      return t('Buttons.Finish');
    }
    return t('Buttons.Cancel');
  };

  const onSaveRequest = async (files: UploadFileInfo[]): Promise<{ fileName: string } | undefined> => {
    try {
      resetFileStatus();
      if (files && files?.length > 1) {
        setFileUploaded(false);
        return;
      }
      if (files && files?.length > 0 && files[0].getRawFile) {
        const file = files?.[0].getRawFile();

        Papa.parse(file, {
          complete: (results: ICsvResultProps) => {
            const userData: IUserProps[] = [];
            let errorData = 0;
            results?.data?.forEach((data) => {
              if (data['Name'] && data['Email Address']) {
                const groups = getGroupIds(data['Group(s)']?.split(','));
                userData.push({
                  fullName: data['Name'],
                  emailAddress: data['Email Address'],
                  groupNames: data['Group(s)'],
                  newGroups: groups.newGroups,
                  groups: groups.groupIds,
                });
              } else {
                errorData += 1;
                updateErrorData({
                  fullName: data['Name'],
                  emailAddress: data['Email Address'],
                  groups: data['Group(s)'],
                  reason: t('OrganisationUsers.BulkAddPopup.MandatoryUser'),
                });
              }
            });

            if (userData.length > 0) {
              setFileUploaded(true);
            } else {
              setInvalidValidationMessage(t('DataSources.FileUpload.StatusMessages.InvalidMinFileSize'));
            }

            setUserData(userData);
            setTotalCount((prev) => userData.length + prev + errorData);
          },
          header: true,
          skipEmptyLines: true,
        });
      }
    } catch {
      return Promise.reject(new Error());
    }
  };

  const updateErrorData = (data: IUserErrorProps) => {
    setFailedCount((prev) => prev + 1);

    setErrorUserData((prev) => {
      const newErrors = [...prev];
      newErrors.push({
        [headerKeys.Name]: data.fullName,
        [headerKeys.Email]: data.emailAddress,
        [headerKeys.Groups]: data.groups,
        [headerKeys.Reason]: data.reason,
      });

      return newErrors;
    });
  };

  const getGroupIds = (selectedGroups?: string[]): IUserGroups => {
    const groupIds: string[] = [];
    const newGroups: string[] = [];

    if (selectedGroups) {
      for (const group of selectedGroups) {
        const apiGroup = allGroups?.find((gr) => gr.label?.toLowerCase()?.trim() === group?.toLowerCase()?.trim());

        if (apiGroup?.value) {
          groupIds.push(apiGroup?.value?.trim());
        } else {
          newGroups.push(group?.trim());
        }
      }
    }

    return { newGroups, groupIds };
  };

  const onRemove = () => {
    resetFileStatus();
  };

  const userErrorColour = () => {
    if (isError) return 'red';
    if (isSuccess) return 'green';
    return 'grey';
  };

  const resetFileStatus = () => {
    setProgressCount(0);
    setFailedCount(0);
    setUserData([]);
    setErrorUserData([]);
    setTotalCount(0);
    setFileUploaded(false);
    setAllNewGroups([]);
  };

  const closeAndReset = () => {
    resetFileStatus();
    setIsImporting(false);
    setIsSuccess(false);
    setIsError(false);
    onClose();
  };

  const downloadCsvFile = (data: unknown[] | UnparseObject<unknown>, fileName: string) => {
    const csvContents = Papa.unparse(data);
    const downloadLink = document.createElement('a');
    downloadLink.href = 'data:text/csv;charset=utf-8,' + encodeURI(csvContents);
    downloadLink.target = '_blank';
    downloadLink.download = `${fileName}.csv`;
    // eslint-disable-next-line testing-library/no-node-access
    downloadLink.click();
  };

  const downloadCsvTemplate = () => {
    const template = {
      fields: [headerKeys.Email, headerKeys.Name, headerKeys.Groups],
      data: [],
    };

    downloadCsvFile(template, 'BulkAddUsers');
  };

  const downloadUnimportedCsv = () => {
    downloadCsvFile(errorUserData, 'UnimportedUsers');
  };

  return (
    <PhlexDialog
      isOpen={isOpen}
      heading={t('OrganisationUsers.BulkAddPopup.Heading')}
      hideClose={isImporting || isSuccess}
      onConfirm={onPrimaryClick}
      onClose={closeAndReset}
      confirmLabel={getPrimaryLabel()}
      closeLabel={getSecondaryLabel()}
      confirmDisabled={getPrimaryLabel() === t('Buttons.Import') ? !fileUploaded : isImporting}
      confirmLoading={false}
    >
      <DialogWrapper>
        {!isImporting && !isSuccess && !isError && (
          <>
            <TextContainer>
              <InfoText>{t('OrganisationUsers.BulkAddPopup.IntroText')}</InfoText>
              <InfoText>{t('OrganisationUsers.BulkAddPopup.RequirementsText')}</InfoText>
            </TextContainer>
            <InfoPanel color="green">
              <DownloadText>
                <PhlexIcon name="download" />
                {t('OrganisationUsers.BulkAddPopup.DownloadText')}
              </DownloadText>
              <PhlexButton label={t('Buttons.Download')} color="secondary" onClick={downloadCsvTemplate} />
            </InfoPanel>
            <AxonUploader
              name={'bulkUserUpload'}
              uploadUrl={onSaveRequest}
              invalidValidationMessage={invalidValidationMessage}
              onRemove={onRemove}
              allowedExtensions={['.csv']}
              isFormSubmitMode={true}
              restrictionMessage={t('OrganisationUsers.BulkAddPopup.UploadRestrictions')}
              showProgressBadges={false}
            />
          </>
        )}

        {(isImporting || isSuccess) && (
          <>
            <ImportSummary>
              <ImportInfo color={isSuccess ? 'green' : 'blue'}>
                <b>{progressCount}</b> {t('OrganisationUsers.BulkAddPopup.ImportedSeparator')} <b>{totalCount}</b>{' '}
                {t('OrganisationUsers.BulkAddPopup.UsersImported')}
              </ImportInfo>
              <ImportInfo color={userErrorColour()}>
                {isError ? (
                  <>
                    <b>{failedCount}</b> {t('OrganisationUsers.BulkAddPopup.UnableToImport')}
                  </>
                ) : (
                  t('OrganisationUsers.BulkAddPopup.NoIssues')
                )}
              </ImportInfo>
            </ImportSummary>
            <ProgressWrapper>
              <UserImport done={isSuccess} />
              <ProgressMsg>
                {isSuccess ? (
                  <CompleteText>{t('OrganisationUsers.BulkAddPopup.ImportComplete')}</CompleteText>
                ) : (
                  t('OrganisationUsers.BulkAddPopup.Importing')
                )}
              </ProgressMsg>
              <ProgressMsg>
                {isSuccess ? t('OrganisationUsers.BulkAddPopup.SuccessMessage') : t('OrganisationUsers.BulkAddPopup.ImportingWarning')}
              </ProgressMsg>
            </ProgressWrapper>
          </>
        )}

        {!isImporting && isError && (
          <>
            <ImportSummary>
              <ImportInfo color="green">
                <b>{progressCount}</b> {t('OrganisationUsers.BulkAddPopup.ImportedSeparator')} <b>{totalCount}</b>{' '}
                {t('OrganisationUsers.BulkAddPopup.UsersImported')}
              </ImportInfo>
              <ImportInfo color="red">
                <b>{failedCount}</b> {t('OrganisationUsers.BulkAddPopup.UnableToImport')}
              </ImportInfo>
            </ImportSummary>
            <TextContainer>
              <InfoText>
                <b>{t('OrganisationUsers.BulkAddPopup.IssuesRequiringAttention')}</b>
              </InfoText>
              <InfoText>{t('OrganisationUsers.BulkAddPopup.MissingUsersText')}</InfoText>
              <InfoText>{t('OrganisationUsers.BulkAddPopup.SkipMissingUsers')}</InfoText>
            </TextContainer>
            <InfoPanel color="amber">
              <DownloadText>
                <PhlexIcon name="download" />
                {t('OrganisationUsers.BulkAddPopup.DownloadMissingCSV')}
              </DownloadText>
              <PhlexButton label={t('Buttons.Download')} color="secondary" onClick={downloadUnimportedCsv} />
            </InfoPanel>
          </>
        )}
      </DialogWrapper>
    </PhlexDialog>
  );
};

export default BulkAddUsersDialog;
