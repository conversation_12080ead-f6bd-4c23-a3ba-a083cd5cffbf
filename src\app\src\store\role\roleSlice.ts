import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { roleApiService, roleDefinitionApiService, permissionApiService, scopeResourceApiService } from 'api';
import { IRoleStore } from './types';
import { RoleModel, RoleModelApiPagedListResult, AppRoleDefinitionPermission, ScopeDataModel } from 'axon-core-api-sdk';

const name = 'role';
const initialState: IRoleStore = {
  isLoadingRoles: null,
  roles: {} as RoleModelApiPagedListResult,
  selectedRole: {} as RoleModel,
  isSelectedRoleLoading: null,
  isRoleDefintionLoading: null,
  roleDefinition: [] as Array<AppRoleDefinitionPermission>,
  isPermissionScopeDataLoading: null,
  permissionScopeData: [] as Array<ScopeDataModel>,
  allRoles: [] as RoleModel[],
  isAllRolesLoading: false,
  scopeResources: [] as ScopeDataModel[],
} as IRoleStore;

export const getFilteredAsync = createAsyncThunk<
  RoleModelApiPagedListResult,
  { organisationName: string; appCodeName: string; limit: number; offset: number; orderBy: string; filter: string }
>(`role/getFilteredAsync`, async ({ organisationName, appCodeName, limit, offset, orderBy, filter }) => {
  const response = await roleApiService.getFiltered(organisationName, appCodeName, filter, orderBy, offset, limit);
  const emptyResult = {} as RoleModelApiPagedListResult;
  return (response?.data?.data as RoleModelApiPagedListResult) ?? emptyResult;
});

export const getRoleByIdAsync = createAsyncThunk<RoleModel, { id: string; organisationName: string; appCodeName: string }>(
  `${name}/getRoleByIdAsync`,
  async ({ id, organisationName, appCodeName }) => {
    const response = await roleApiService.getById(id, organisationName, appCodeName);
    const result = {} as RoleModel;
    return response?.data?.data ?? result;
  }
);

export const getRoleDefinitionByAppCodeAsync = createAsyncThunk<Array<AppRoleDefinitionPermission>, { appCodeName: string }>(
  `${name}/getRoleDefinitionByAppCodeAsync`,
  async ({ appCodeName }) => {
    const response = await roleDefinitionApiService.getByAppCode(appCodeName);
    const result = [] as Array<AppRoleDefinitionPermission>;
    return response?.data?.data ?? result;
  }
);

export const getRoleDefinitionByOrgAndAppCodeAsync = createAsyncThunk<
  Array<AppRoleDefinitionPermission>,
  { orgCodeName: string; appCodeName: string }
>(`${name}/getRoleDefinitionByOrgAndAppCodeAsync`, async ({ orgCodeName, appCodeName }) => {
  const response = await roleDefinitionApiService.getByOrganisationAndAppCode(orgCodeName, appCodeName);
  const result = [] as Array<AppRoleDefinitionPermission>;
  return response?.data?.data ?? result;
});

export const getScopesAndResourcesForOrganisationAsync = createAsyncThunk<
  Array<ScopeDataModel>,
  { organisationName: string; appCodeName: string }
>(`${name}/getScopesAndResourcesForOrganisationAsync`, async ({ organisationName, appCodeName }) => {
  const response = await permissionApiService.getScopesAndResourcesForOrganisation(organisationName, appCodeName);
  const result = [] as Array<ScopeDataModel>;
  return response?.data?.data ?? result;
});

export const getAllRolesAsync = createAsyncThunk<RoleModel[], { organisationName: string; appCodeName: string }>(
  `${name}/getAllRolesAsync`,
  async ({ organisationName, appCodeName }) => {
    const response = await roleApiService.getAllRoles(organisationName, appCodeName);
    const result = [] as RoleModel[];
    return response?.data?.data ?? result;
  }
);

export const getScopeResourcesForRoleAsync = createAsyncThunk<
  ScopeDataModel[],
  { roleId: string; organisationName: string; appCodeName: string }
>(`${name}/getScopeResourcesForRoleAsync`, async ({ roleId, organisationName, appCodeName }) => {
  const response = await scopeResourceApiService.getScopeResourcesForRole(roleId, organisationName, appCodeName);
  const result = [] as ScopeDataModel[];
  return response?.data?.data ?? result;
});

const rolePermissionsSlice = createSlice({
  name: name,
  initialState,
  reducers: {
    resetRoleStore(state: IRoleStore) {
      state.scopeResources = [];
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getFilteredAsync.pending, (state) => {
      state.isLoadingRoles = true;
    });
    builder.addCase(getFilteredAsync.fulfilled, (state, action: PayloadAction<RoleModelApiPagedListResult>) => {
      state.isLoadingRoles = false;
      state.roles = action.payload;
    });
    builder.addCase(getFilteredAsync.rejected, (state) => {
      state.isLoadingRoles = false;
    });
    builder.addCase(getRoleByIdAsync.pending, (state) => {
      state.isSelectedRoleLoading = true;
    });
    builder.addCase(getRoleByIdAsync.fulfilled, (state, action: PayloadAction<RoleModel>) => {
      state.isSelectedRoleLoading = false;
      state.selectedRole = action.payload;
    });
    builder.addCase(getRoleByIdAsync.rejected, (state) => {
      state.isSelectedRoleLoading = false;
    });
    builder.addCase(getRoleDefinitionByAppCodeAsync.pending, (state) => {
      state.isRoleDefintionLoading = true;
    });
    builder.addCase(getRoleDefinitionByAppCodeAsync.fulfilled, (state, action: PayloadAction<Array<AppRoleDefinitionPermission>>) => {
      state.isRoleDefintionLoading = false;
      state.roleDefinition = action.payload;
    });
    builder.addCase(getRoleDefinitionByAppCodeAsync.rejected, (state) => {
      state.isRoleDefintionLoading = false;
    });
    builder.addCase(getRoleDefinitionByOrgAndAppCodeAsync.pending, (state) => {
      state.isRoleDefintionLoading = true;
    });
    builder.addCase(getRoleDefinitionByOrgAndAppCodeAsync.fulfilled, (state, action: PayloadAction<Array<AppRoleDefinitionPermission>>) => {
      state.isRoleDefintionLoading = false;
      state.roleDefinition = action.payload;
    });
    builder.addCase(getRoleDefinitionByOrgAndAppCodeAsync.rejected, (state) => {
      state.isRoleDefintionLoading = false;
      state.roleDefinition = [];
    });
    builder.addCase(getScopesAndResourcesForOrganisationAsync.pending, (state) => {
      state.isPermissionScopeDataLoading = true;
    });
    builder.addCase(getScopesAndResourcesForOrganisationAsync.fulfilled, (state, action: PayloadAction<Array<ScopeDataModel>>) => {
      state.isPermissionScopeDataLoading = false;
      state.permissionScopeData = action.payload;
    });
    builder.addCase(getScopesAndResourcesForOrganisationAsync.rejected, (state) => {
      state.isPermissionScopeDataLoading = false;
    });
    builder.addCase(getAllRolesAsync.pending, (state) => {
      state.isAllRolesLoading = true;
    });
    builder.addCase(getAllRolesAsync.fulfilled, (state, action: PayloadAction<RoleModel[]>) => {
      state.isAllRolesLoading = false;
      state.allRoles = action.payload;
    });
    builder.addCase(getAllRolesAsync.rejected, (state) => {
      state.isAllRolesLoading = false;
    });
    builder.addCase(getScopeResourcesForRoleAsync.fulfilled, (state, action: PayloadAction<Array<ScopeDataModel>>) => {
      state.isRoleDefintionLoading = false;
      state.scopeResources = action.payload;
    });
    builder.addCase(getScopeResourcesForRoleAsync.rejected, (state) => {
      state.scopeResources = [];
    });
    builder.addCase(getScopeResourcesForRoleAsync.pending, (state) => {
      state.scopeResources = [];
    });
  },
});

export const { resetRoleStore } = rolePermissionsSlice.actions;
export default rolePermissionsSlice.reducer;
