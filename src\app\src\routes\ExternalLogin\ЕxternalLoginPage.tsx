import React, { FunctionComponent, useEffect } from 'react';
import { organisationApiService } from 'api';
import { Navigate, useParams, useSearchParams } from 'react-router-dom';
import { withComplexRestrictedPermissions } from 'hoc';
import { PERMISSIONS } from 'constants/permissions';
import { routes } from 'routes';

const ExternalLoginPage: FunctionComponent = () => {
  const { codeName, appCodeName } = useParams<{ codeName: string; appCodeName: string }>();
  const [searchParams] = useSearchParams();
  const postLoginRedirect = searchParams.get('postLoginRedirect');
  const domain = new URL(postLoginRedirect ?? '');

  useEffect(() => {
    if (!codeName || !appCodeName || !postLoginRedirect) {
      <Navigate to={routes.notFound} />;
      return;
    }
    organisationApiService
      .issueCode(codeName, { appCodeName: appCodeName })
      .then((res) => {
        if (res.data.data && res.data.data.code) {
          window.location.href = `${domain.origin}/axon-auth/sign-in?code=${res.data.data.code}&redirectUrl=${postLoginRedirect}`;
        }
      })
      .catch(() => <Navigate to={routes.notFound} />);
  }, [codeName, appCodeName, postLoginRedirect, domain.origin]);

  return <></>;
};

export default withComplexRestrictedPermissions(
  ExternalLoginPage,
  {
    permissionRules: [{ permissions: [PERMISSIONS.ViewOrganisation, PERMISSIONS.ViewApplication], accessType: 'all' }],
  },
  true
);
