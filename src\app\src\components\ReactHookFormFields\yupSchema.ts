/*** AUTO-GENERATED FILE - EDITS WILL BE OVERWRITTEN ***/

import * as yup from 'yup';
import { t } from 'i18next';

export const getNonRegexCompliantCharactersErrorMessage = (regex: RegExp, field: string): string => {
  const invalidCharacters = field.split('').filter((char) => !regex.test(char));
  const uniqueCharacters = [...new Set(invalidCharacters)];
  return t('CRUDTemplate.Forms.InvalidCharacters', { characterList: uniqueCharacters.join(', ') });
};

const textMax: number = 100;
const textMin: number = 2;
const textMinRegex = /.{2,}/;
const textAreaMax: number = 2500;
const textRegex = /^[\p{L}\p{N}\p{M}.,'\- ]+$/u;

const getYupText = (required: boolean, fieldName: string) => {
  const yupText = yup
    .string()
    .test('is-regex-compliant', function (value) {
      if (!value) return true;
      if (!textRegex.test(value)) {
        return this.createError({
          message: getNonRegexCompliantCharactersErrorMessage(textRegex, value),
        });
      }
      return true;
    })
    .max(textMax, t('CRUDTemplate.Forms.InsertMaxCharacters', { number: textMax.toString() }))
    .matches(textMinRegex, {
      excludeEmptyString: true,
      message: t('CRUDTemplate.Forms.InsertMinCharacters', { number: textMin.toString() }),
    });

  if (required) {
    return yupText.required(t('CRUDTemplate.Forms.RequiredField', { field: fieldName }));
  }
  return yupText;
};

const getYupTextArea = (required: boolean, fieldName: string): boolean | yup.StringSchema => {
  const yupText = yup
    .string()
    .test('is-regex-compliant', function (value) {
      if (!value) return true;
      if (!textRegex.test(value)) {
        return this.createError({
          message: getNonRegexCompliantCharactersErrorMessage(textRegex, value),
        });
      }
      return true;
    })
    .max(textAreaMax, t('CRUDTemplate.Forms.InsertMaxCharacters', { number: textAreaMax.toString() }))
    .matches(textMinRegex, {
      excludeEmptyString: true,
      message: t('CRUDTemplate.Forms.InsertMinCharacters', { number: textMin.toString() }),
    });

  if (required) {
    return yupText.required(t('CRUDTemplate.Forms.RequiredField', { field: fieldName }));
  }

  return yupText;
};

const getYupUri = (required: boolean, fieldName: string) => {
  const yupUri = yup.string().url(t('CRUDTemplate.Forms.ValidField', { field: fieldName }));

  if (required) {
    return yupUri.required(t('CRUDTemplate.Forms.RequiredField', { field: fieldName }));
  }
  return yupUri;
};

const getYupEmailAddress = (required: boolean, fieldName: string) => {
  const yupEmailAddress = yup.string().email(t('CRUDTemplate.Forms.ValidField', { field: fieldName }));

  if (required) {
    return yupEmailAddress.required(t('CRUDTemplate.Forms.RequiredField', { field: fieldName }));
  }
  return yupEmailAddress;
};

const getYupDropdown = (required: boolean, fieldName: string) => {
  const yupDropdown = yup.string();

  if (required) {
    return yupDropdown.required(t('CRUDTemplate.Forms.RequiredField', { field: fieldName }));
  }
  return yupDropdown;
};

const getYupMultiSelect = (required: boolean, fieldName: string) => {
  const yupMultiselect = yup.array().of(
    yup.object().shape({
      label: yup.string(),
      value: yup.string(),
    })
  );

  if (required) {
    return yupMultiselect.required(t('CRUDTemplate.Forms.RequiredField', { field: fieldName }));
  }
  return yupMultiselect;
};

export const getYupField = (inputType: string, inputName: string, required: boolean): any => {
  switch (inputType) {
    case 'TEXT':
      return getYupText(required, inputName);
    case 'TEXT_AREA':
      return getYupTextArea(required, inputName);
    case 'URI':
      return getYupUri(required, inputName);
    case 'DROPDOWN':
      return getYupDropdown(required, inputName);
    case 'MULTISELECT':
      return getYupMultiSelect(required, inputName);
    case 'EMAIL':
      return getYupEmailAddress(required, inputName);
    case 'SWITCH':
      return getYupEmailAddress(required, inputName);
    default:
      return getYupText(required, inputName);
  }
};
