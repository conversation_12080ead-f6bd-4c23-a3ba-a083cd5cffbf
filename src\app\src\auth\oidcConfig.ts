import { UserManager, WebStorageStateStore } from 'oidc-client-ts';
import { environmentVariableSrv as env } from 'shared/services';

export type OidcProvider = {
  authority: string;
  client_id: string;
};

let oidcClient: UserManager | null = null;

export const setOidcClient = ({ authority, client_id }: OidcProvider) => {
  oidcClient = new UserManager({
    authority,
    client_id,
    automaticSilentRenew: false,
    post_logout_redirect_uri: `${env.getVariable('AXON_CORE_UI_PUBLIC_URL')}sign-out`,
    redirect_uri: env.getVariable('AXON_CORE_UI_PUBLIC_URL'),
    scope: 'email openid profile User.Read',
    userStore: new NonPersistingUserStore(),
  });
};

//This will not write anything to either session or local storage
//It is to be used for the userStore on a UserManager.
//This means that the only time a token is
//available is when reading it from the redirect url in the signin flow
//after this point it is not persisted.
class NonPersistingUserStore extends WebStorageStateStore {
  async set(key: string, value: string): Promise<void> {
    return;
  }
}

export const getOidcClient = () => oidcClient;
