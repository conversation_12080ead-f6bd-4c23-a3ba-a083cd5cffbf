import React from 'react';

import { useAuthStore, useGlobalStore } from 'store';
import { authService } from 'shared/services';
import { Navigate } from 'react-router-dom';
import { routes } from 'routes';
import { AllowedOrganisationAccessLevel } from 'constants/permissions';

type AccessType = 'all' | 'any';

type WithRestrictedPermissionsProps = {
  permissions: string[];
  accessType: AccessType;
  redirect: boolean;
};

type SimpleRestrictionRule = {
  permissions: string[];
  accessType: AccessType;
};

type RestrictionRuleSet = {
  permissionRules: SimpleRestrictionRule[];
};

export function withRestrictedPermissions<T>(
  WrappedComponent: React.ComponentType<T>,
  permissions: string[],
  accessType: AccessType = 'all',
  redirect = false,
  allowedOrganisationAccessLevels?: AllowedOrganisationAccessLevel[]
) {
  return withComplexRestrictedPermissions(
    WrappedComponent,
    { permissionRules: [{ permissions: permissions, accessType: accessType }] },
    redirect,
    allowedOrganisationAccessLevels
  );
}

export function withComplexRestrictedPermissions<T>(
  WrappedComponent: React.ComponentType<T>,
  restrictions: RestrictionRuleSet,
  redirect = false,
  allowedOrganisationAccessLevels?: AllowedOrganisationAccessLevel[]
) {
  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component';

  const ComponentWithRestrictedPermissions = (props: Omit<T, keyof WithRestrictedPermissionsProps>) => {
    const { permissions: globalPermissions, isLoading } = useAuthStore();
    const { userOrganisation } = useGlobalStore();

    if (isLoading || !globalPermissions) return null;

    if (
      allowedOrganisationAccessLevels &&
      !allowedOrganisationAccessLevels.find((accessLevel: AllowedOrganisationAccessLevel) => accessLevel === userOrganisation.accessLevel)
    ) {
      return redirect ? <Navigate to={routes.notFound} /> : null;
    }

    for (const rule of restrictions.permissionRules) {
      if (!authService.hasAccess(globalPermissions, rule.permissions, rule.accessType)) {
        return redirect ? <Navigate to={routes.notFound} /> : null;
      }
    }

    return <WrappedComponent {...(props as any)} />;
  };

  ComponentWithRestrictedPermissions.displayName = `withTheme(${displayName})`;

  return ComponentWithRestrictedPermissions;
}
