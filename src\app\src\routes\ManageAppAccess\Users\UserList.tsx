import React, { FunctionComponent, ReactElement, useEffect, useState } from 'react';
import { PhlexDialog } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';
import RolesTable from 'components/Tables/ManageAppAccess/RoleTable';
import { SortDescriptor } from '@progress/kendo-data-query';
import { GridCellProps, GridPageChangeEvent, GridSortChangeEvent } from '@progress/kendo-react-grid';
import { Page } from '@progress/kendo-react-dropdowns';
import components from './UserList.styles';
import tableComponents from 'components/Tables/Table.styles';
import { useUsers } from 'store';
import { toast } from 'react-toastify';
import { userApiService } from 'api';
import EditUser from './EditUser';
import { SortableTableColumn } from 'components/Tables/SortableTableColumn';
import { AppUserModel, AppUserModelApiPagedListResult } from 'axon-core-api-sdk';
import { DEFAULT_PAGE_SIZE } from 'constants/dataScreen';

const UserList: FunctionComponent<{
  organisationCodeName: string;
  appCodeName: string;
  filter: string | null;
  searchPhrase: string | null;
}> = ({ organisationCodeName, appCodeName, filter, searchPhrase }) => {
  const { t } = useTranslation();
  const initialSort: Array<SortDescriptor> = [{ field: 'user.Name', dir: 'asc' }];
  const [sort, setSort] = useState(initialSort);
  const [pagination, setPagination] = useState<Page>({ take: DEFAULT_PAGE_SIZE, skip: 0 });
  const [confirmationDialogVisible, setConfirmationDialogVisible] = useState(false);
  const [deleteUser, setDeleteUser] = useState<GridCellProps>();
  const [userDetailDialogOpen, setUserDetailDialogOpen] = useState<boolean>(false);
  const [allUsers, setAllUsers] = useState<AppUserModelApiPagedListResult>();

  const { TableRowHeader, TableRowDesc } = components;
  const { TableIcon } = tableComponents;

  const {
    users,
    selectedUser,
    actions: { getFilteredAppUsers, getAppUserById },
  } = useUsers();

  useEffect(() => {
    setAllUsers(users);
  }, [users]);

  const onSortChange = (e: GridSortChangeEvent) => {
    setPagination({ take: DEFAULT_PAGE_SIZE, skip: 0 });
    setSort(e.sort?.filter((sortFilter: SortDescriptor) => sortFilter.field !== ''));
  };

  const onPageChange = (event: GridPageChangeEvent) => {
    setPagination(event.page);
  };

  const refreshCurrentPage = () => {
    if (organisationCodeName) {
      const regex = new RegExp(t('Forms.SafeCharactersRegex'));
      if (searchPhrase && !regex.test(searchPhrase)) {
        setAllUsers(undefined);
        return;
      }

      getFilteredAppUsers(
        organisationCodeName,
        appCodeName,
        pagination.take,
        pagination.skip,
        sort.length === 0 ? 'user.Name desc' : `${sort[0].field} ${sort[0].dir}`,
        filter ?? ''
      );
    }
  };

  useEffect(() => {
    if (filter) {
      setSort(initialSort);
      setPagination({ take: DEFAULT_PAGE_SIZE, skip: 0 });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(refreshCurrentPage, [organisationCodeName, appCodeName, sort, pagination, filter]);

  const rowClick = (cell: GridCellProps, dataItemProp: string, field: string): ReactElement => {
    const text = cell.dataItem[dataItemProp]?.map((d: any) => {
      return d[field];
    });
    return (
      <td title={text?.join(', ')} onClick={() => userDetail(cell.dataItem['email'])}>
        {text?.join(', ')}
      </td>
    );
  };

  const userDetail = (email: string) => {
    getAppUserById(organisationCodeName, appCodeName, email);
    setUserDetailDialogOpen(true);
  };

  const removeCell = (gridCell: GridCellProps) => {
    if (gridCell.dataItem['hasDirectRole'] === false) {
      return null;
    }
    return (
      <TableIcon title={t('UserList.RemoveUserAccessHeading')} onClick={() => deleteUserPopup(gridCell)}>
        close
      </TableIcon>
    );
  };

  const deleteUserPopup = (gridCell: GridCellProps) => {
    setConfirmationDialogVisible(true);
    setDeleteUser(gridCell);
  };

  const itemTextCell = (cell: GridCellProps, dataItemProp: string): ReactElement => {
    const text = cell.dataItem[dataItemProp];
    const email = cell.dataItem['email'];

    return (
      <td title={email} onClick={() => userDetail(cell.dataItem['email'])}>
        <TableRowHeader>{text}</TableRowHeader>
        <TableRowDesc>{email}</TableRowDesc>
      </td>
    );
  };

  const columns: SortableTableColumn[] = [
    {
      identifier: 'user.Name',
      title: t('UserList:Name'),
      cell: (cell: GridCellProps) => itemTextCell(cell, 'userName'),
      sortable: true,
    },
    {
      identifier: 'roles',
      title: t('UserList.Roles'),
      cell: (cell: GridCellProps) => rowClick(cell, 'roles', 'roleName'),
      width: '200px',
      sortable: false,
      headerClassName: 'no-sort',
    },
    {
      identifier: 'groups',
      title: t('UserList.Groups'),
      cell: (cell: GridCellProps) => rowClick(cell, 'groups', 'groupName'),
      width: '160px',
      sortable: false,
      headerClassName: 'no-sort',
    },
    { identifier: '', title: '', cell: removeCell, width: '40px', sortable: false, headerClassName: 'no-sort' },
  ];

  const deleteUserRequest = () => {
    userApiService
      .deleteAppUser(appCodeName, organisationCodeName, deleteUser?.dataItem['email'])
      .then(() => {
        setConfirmationDialogVisible(false);
        getFilteredAppUsers(organisationCodeName, appCodeName, 20, 0, 'user.Name asc', filter ?? '');
        toast.success(t('UserList.DeleteUserSuccess'));
      })
      .catch(() => {
        toast.error(t('Shared.ApiFailedErrorMessage'));
      });
  };

  return (
    <div data-testid="group-list-wrapper">
      <RolesTable
        isLoading={false}
        data={
          allUsers?.data?.map((user: AppUserModel) => ({
            ...user,
            'user.Name': user.userName,
          })) ?? []
        }
        uniqueIdField={'id'}
        sortable={true}
        pageable={true}
        columns={columns}
        onSortChange={onSortChange}
        onPageChange={onPageChange}
        sort={sort}
        total={allUsers?.paging?.totalItemCount}
        take={pagination.take}
        skip={pagination.skip}
        nameOfRows={t('UserList.Users')}
        noRecordsMessage={t('Forms.Placeholders.NoUsersFound')}
      />

      {selectedUser && (
        <EditUser
          selectedUser={selectedUser}
          editUserDialogOpen={userDetailDialogOpen}
          organisationCodeName={organisationCodeName}
          appCodeName={appCodeName}
          filter={filter}
          onAddUserDialogClose={() => setUserDetailDialogOpen(false)}
          onSubmit={refreshCurrentPage}
        ></EditUser>
      )}

      <PhlexDialog
        confirmType="destructive"
        closeLabel={t('Buttons.Cancel')}
        confirmLabel={t('Buttons.Remove')}
        isOpen={confirmationDialogVisible}
        heading={t('UserList.RemoveUserAccessHeading')}
        message={t('UserList.RemoveUserAccessDescription', { userName: deleteUser?.dataItem['userName'] })}
        onClose={() => setConfirmationDialogVisible(false)}
        onConfirm={deleteUserRequest}
      />
    </div>
  );
};

export default UserList;
