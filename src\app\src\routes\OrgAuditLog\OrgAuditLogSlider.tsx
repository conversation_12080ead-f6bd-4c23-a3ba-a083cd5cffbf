import { <PERSON>I<PERSON>, PhlexSwitch, PhlexTable, PhlexTabStrip, TableColumn } from 'phlex-core-ui';
import React, { FunctionComponent, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import components from './OrgAuditLogSlider.styles';
import { toDateTimeFormat, isValidDate } from '../../shared/services';
import { getFormattedPropertyName } from 'shared/utils/getFormattedPropertyName';
import { SwitchChangeEvent } from '@progress/kendo-react-inputs';

type OrgAuditLogSliderProps = {
  currentRow?: DataItem;
};

type ObjectType = {
  name?: string | number | boolean;
  value?: string | number | boolean | null;
};

type TargetType = {
  name?: string | number | boolean;
  changed?: string | number | boolean;
  old?: string | number | boolean;
  new?: string | number | boolean;
};

const OrgAuditLogSlider: FunctionComponent<OrgAuditLogSliderProps> = ({ currentRow }) => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState(0);
  const [environmentData, setEnvironmentData] = useState<ObjectType[]>([]);
  const [targetData, setTargetData] = useState<TargetType[]>([]);
  const [switchTargetData, setSwitchTargetData] = useState<TargetType[]>([]);
  const [extraFields, setExtraFields] = useState<ObjectType[]>([]);
  const [activityFields, setActivityFields] = useState<ObjectType[]>([]);
  const [showChangedValues, setShowChangedValues] = useState<boolean>(false);

  const environmentColumns: TableColumn[] = [
    { identifier: 'name', title: t('AuditLog.Title'), showTooltip: true, width: '250px' },
    { identifier: 'value', title: t('AuditLog.Value'), showTooltip: true },
  ];

  const targetColumns: TableColumn[] = [
    { identifier: 'name', title: t('AuditLog.Title'), showTooltip: true },
    { identifier: 'changed', title: t('AuditLog.ChangedValue'), showTooltip: true },
    { identifier: 'old', title: t('AuditLog.OldValue'), showTooltip: true },
    { identifier: 'new', title: t('AuditLog.NewValue'), showTooltip: true },
  ];

  useEffect(() => {
    if (currentRow) {
      setActivityTabData(currentRow);
      setTargetTabData(currentRow);
      setExtraFieldsData(currentRow);
      setActivityFieldsData(currentRow);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentRow]);

  const setExtraFieldsData = (currentRow: DataItem) => {
    const extraFields: ObjectType[] = [];
    extraFields.push(
      {
        name: t('AuditLog.Tenant'),
        value: currentRow.Tenant ? currentRow.Tenant : '',
      },
      {
        name: t('AuditLog.IpAddress'),
        value: currentRow.IpAddress ? currentRow.IpAddress : '',
      },
      {
        name: t('AuditLog.CorrelationId'),
        value: currentRow.CorrelationId ? currentRow.CorrelationId : '',
      }
    );
    setExtraFields(extraFields);
  };

  const setActivityFieldsData = (currentRow: DataItem) => {
    const activitiyFields: ObjectType[] = [];
    activitiyFields.push(
      {
        name: t('AuditLog.Date'),
        value: toDateTimeFormat(String(currentRow.StartDate)),
      },
      {
        name: t('AuditLog.EventType'),
        value: currentRow.EventType,
      },
      {
        name: t('AuditLog.EventCategory'),
        value: currentRow.EventCategory,
      },
      {
        name: t('AuditLog.EventDescription'),
        value: currentRow.EventDescription,
      },
      {
        name: t('AuditLog.UserEmail'),
        value: currentRow.UserEmail || currentRow.UserId,
      },
      {
        name: t('AuditLog.EventOutcome'),
        value: currentRow.EventOutcome,
      }
    );
    setActivityFields(activitiyFields);
  };

  const setActivityTabData = (currentRow: DataItem) => {
    const currentRowEnvironment: any = currentRow?.Environment ?? {};
    const environmentFields: ObjectType[] = Object.keys(currentRowEnvironment).map((group) => ({
      name: getFormattedPropertyName(group),
      value: currentRowEnvironment[group],
    }));
    setEnvironmentData(environmentFields);
  };

  const setTargetTabData = (currentRow: DataItem) => {
    if (!currentRow.Target) {
      setTargetData([]);
      setSwitchTargetData([]);
      return;
    }

    const currentRowTarget: any = currentRow?.Target;

    const topLevelKeys = Object.keys(currentRowTarget.New ?? {})
      .concat(Object.keys(currentRowTarget.Old ?? {}))
      .filter((value, index, array) => array.indexOf(value) === index);

    const target = topLevelKeys.map((key) => {
      return {
        name: getFormattedPropertyName(key),
        old:
          currentRowTarget.Old && !currentRow.EventType?.toString().toLowerCase().includes('create')
            ? getFormattedTargetValue(currentRowTarget.Old, key)
            : null,
        new: currentRowTarget.New ? getFormattedTargetValue(currentRowTarget.New, key) : null,
        changed:
          currentRowTarget.Changed && !currentRow.EventType?.toString().toLowerCase().includes('create')
            ? getFormattedTargetValue(currentRowTarget.Changed, key)
            : null,
      };
    });

    setTargetData(target);
    setShowChangedValues(false);
    setSwitchTargetData(target.filter((tr) => tr.old && tr.old !== tr.new));
  };

  const getFormattedTargetValue = (target: any, key: string): any => {
    const value = target[key];
    if (value === undefined) {
      return null;
    }
    if (isValidDate(value)) {
      return toDateTimeFormat(value);
    }
    if (typeof value === 'object' || Array.isArray(value)) {
      return JSON.stringify(value);
    }
    return String(value);
  };

  const targetValueChange = (event: SwitchChangeEvent) => {
    setShowChangedValues(event.value);

    if (!event.value) {
      setSwitchTargetData(targetData.filter((tr) => tr.old && tr.old !== tr.new));
    } else {
      setSwitchTargetData(targetData);
    }
  };

  const { Row, TabSubHeader, SwitchRow, SwitchLabel } = components;

  return (
    <PhlexTabStrip
      keepTabsMounted
      selected={selectedTab}
      heightOffset="8.25rem"
      onSelect={(e: { selected: React.SetStateAction<number> }) => setSelectedTab(e.selected)}
      tabs={[
        {
          name: t('AuditLog.ActivityTab'),
          content: (
            <>
              <Row>
                <TabSubHeader>{t('AuditLog.Activity')}</TabSubHeader>
                <PhlexTable
                  isLoading={false}
                  data={activityFields}
                  uniqueIDField={'name'}
                  sortable={false}
                  pageable={false}
                  columns={environmentColumns}
                />
              </Row>
              <Row>
                <TabSubHeader>{t('AuditLog.Environment')}</TabSubHeader>
                <PhlexTable
                  isLoading={false}
                  data={environmentData}
                  uniqueIDField={'name'}
                  sortable={false}
                  pageable={false}
                  columns={environmentColumns}
                />
              </Row>
              <Row>
                <TabSubHeader>{t('AuditLog.ExtraFields')}</TabSubHeader>
                <PhlexTable
                  isLoading={false}
                  data={extraFields}
                  uniqueIDField={'name'}
                  sortable={false}
                  pageable={false}
                  columns={environmentColumns}
                />
              </Row>
            </>
          ),
        },
        {
          name: t('AuditLog.TargetTab'),
          content: (
            <>
              <SwitchRow>
                <SwitchLabel>{t('AuditLog.ShowAll')}</SwitchLabel>
                <PhlexSwitch
                  checked={showChangedValues}
                  onChange={(event) => {
                    targetValueChange(event);
                  }}
                />
              </SwitchRow>

              <PhlexTable
                isLoading={false}
                noRecordsMessage={!showChangedValues ? t('AuditLog.NoChangedValues') : ''}
                data={switchTargetData}
                uniqueIDField={'name'}
                sortable={false}
                pageable={false}
                columns={targetColumns}
              />
            </>
          ),
        },
      ]}
    />
  );
};

export default OrgAuditLogSlider;
