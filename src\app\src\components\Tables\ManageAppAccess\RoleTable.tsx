import React, { FunctionComponent } from 'react';

import { SortDescriptor } from '@progress/kendo-data-query';
import { GridPageChangeEvent, GridSortChangeEvent } from '@progress/kendo-react-grid';

import components from './RoleTable.styles';
import { PagerProps } from '@progress/kendo-react-data-tools';
import { PhlexTablePager } from 'phlex-core-ui';
import { SortableTableColumn } from '../SortableTableColumn';

type TableProps = {
  data: any[];
  onPageChange?: (event: GridPageChangeEvent) => void;
  onSortChange?: (e: GridSortChangeEvent) => void;
  sortable?: boolean;
  pageable?: boolean;
  total?: number;
  skip?: number;
  take?: number;
  isLoading?: boolean;
  columns: SortableTableColumn[];
  sort?: SortDescriptor[];
  uniqueIdField: string;
  nameOfRows: string;
  noRecordsMessage: string;
};

const RolesTable: FunctionComponent<TableProps> = ({
  data,
  onPageChange,
  onSortChange,
  sortable,
  pageable,
  total,
  skip,
  take,
  isLoading,
  columns,
  sort,
  uniqueIdField,
  nameOfRows,
  noRecordsMessage,
}: TableProps) => {
  const { Table } = components;

  const tablePager = (p: PagerProps) => <PhlexTablePager {...p} nameOfRows={nameOfRows} />;

  return (
    <Table
      isLoading={isLoading}
      data={data}
      columns={columns}
      uniqueIDField={uniqueIdField}
      sortable={sortable}
      sort={sort}
      total={total}
      onSortChange={onSortChange}
      skip={skip}
      take={take}
      pageable={pageable}
      pager={tablePager}
      onPageChange={onPageChange}
      noRecordsMessage={noRecordsMessage}
    ></Table>
  );
};

export default RolesTable;
