import { useSelector } from 'react-redux';
import { IMyOrganisationState, IMyOrganisationStore } from './types';
import { RootState, useAppDispatch } from 'store/store';
import slice, { getAppsByOrganisationAsync, getOrganisationsAsync } from './myOrganisationSlice';

export const myOrganisationSelector = (state: RootState): IMyOrganisationStore => state.myOrganisation;

export const useMyOrganisationStore = (): IMyOrganisationState => {
  const dispatch = useAppDispatch();
  const getAppsByOrganisation = (organisationId = '', searchPhrase = '') =>
    dispatch(getAppsByOrganisationAsync({ organisationId, searchPhrase }));
  const { isLoading, myApps, organisations } = useSelector(myOrganisationSelector);
  const getOrganisations = () => dispatch(getOrganisationsAsync());

  return {
    isLoading,
    myApps,
    organisations,
    actions: {
      getAppsByOrganisation,
      getOrganisations,
    },
  };
};

export default {
  name: slice ? slice.name : 'myOrganisation',
  reducer: slice,
};
