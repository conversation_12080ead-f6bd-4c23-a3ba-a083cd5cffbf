/*** AUTO-GENERATED FILE - EDITS WILL BE OVERWRITTEN ***/

import { TableColumn } from 'phlex-core-ui';

export interface SortableTableColumn extends TableColumn {
  sortable?: boolean;
}

export enum FilterTypes {
  TEXT_INPUT,
  DATE,
  DROPDOWN,
}

export type ConfigColumn = {
  name: string;
  label: string;
  type: string;
  width?: string;
  sortable?: boolean;
  filterType?: FilterTypes;
};

export type Config = {
  text: {
    addItemTitle: string;
    editItemTitle: string;
    deleteItemTitle: string;
    nameOfRows: string;
    deleteConfirmationMessage: string;
    searchPlaceholder: string;
  };
  columns: ConfigColumn[];
  formFields: any[];
};
