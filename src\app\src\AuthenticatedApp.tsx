import React, { FunctionComponent, useEffect, Fragment, useState, useCallback, Suspense } from 'react';
import { Routes, Route, Navigate, useParams, useNavigate } from 'react-router-dom';

import { PhlexFormSkeleton, PhlexInfoPanel } from 'phlex-core-ui';

import {
  AppsPage,
  DashboardPage,
  OrganisationsPage,
  CreateNewOrganisationAppsPage,
  OrganisationPage,
  CreateNewOrganisationPage,
  ManageAppPage,
  MyAppPage,
  SignOutPage,
  MyOrganisationPage,
  OrganisationDisabledPage,
  NotFoundPage,
  ForbiddenPage,
  routes,
  ManageOrganisationPage,
  OrganisationUsersPage,
  ManageAppAccessPage,
  ManageAxonAccessPage,
  CreateNewAppPage,
  OrgAppSettingsPage,
  OrgAuditLogPage,
  OrgAppActionsPage,
  APP_ACTIONS_NESTED_ROUTES,
  ExposedAppPage,
  ExternalLoginPage,
} from 'routes';
import { NavigationBar } from 'components';
import { ChatResize, Content } from 'global-styles';
import { useAuthStore, useGlobalStore } from './store';
import AppRedirect from 'routes/AppRedirect/AppRedirect';
import AutoLogout from 'components/AutoLogout/AutoLogout';
import { Panel, PanelGroup } from 'react-resizable-panels';
import apiService from 'api/userAccessApiService';
import { CHAT_PERMISSIONS } from 'constants/permissions';
import { AXON_CHAT } from 'constants/app';
import { RouteParamSetter } from 'axon-core-ui-shared';
import EditOrganisationAppsPage from 'routes/EditOrganisationApps/EditOrganisationAppsPage';
import useRemoteChat from 'hooks/useRemoteChat';
import { useAppFromUrl } from 'hooks/shared';
import { t } from 'i18next';
import { RemoteModule } from 'routes/OrgAppActions/RemoteLinks';

const App: FunctionComponent = () => {
  const {
    selectedOrganisation,
    isLoading,
    actions: { loadOrganisationsAppsAndHomeOrganisation, loadUserOrganisation },
  } = useGlobalStore();

  const {
    isLoading: isLoadingAuth,
    actions: { getAllPermissions, updatePermissions },
  } = useAuthStore();

  const [isChatEnabled, setIsChatEnabled] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);
  const [chatExpanded, setChatExpanded] = useState(false);
  const [isLoadingChatPermissions, setIsLoadingChatPermissions] = useState<boolean>(true);
  const [failedFetchingRemote, RemoteChat] = useRemoteChat();
  const appName = useAppFromUrl();
  const navigate = useNavigate();

  useEffect(() => {
    loadCalls();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setIsChatEnabled(false);
    isChatEnabledForUser().then((result) => {
      setIsChatEnabled(result);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedOrganisation]);

  const isChatEnabledForUser = async (): Promise<boolean> => {
    if (!selectedOrganisation?.codeName) {
      setIsLoadingChatPermissions(false);
      return false;
    }

    return apiService
      .validateCurrentUserPermissionsAccess({
        permissions: [
          CHAT_PERMISSIONS.AccessChat,
          CHAT_PERMISSIONS.ManageDataSource,
          CHAT_PERMISSIONS.ViewDataSource,
          CHAT_PERMISSIONS.ManageTag,
        ],
        appCodeName: AXON_CHAT,
        orgCodeName: selectedOrganisation?.codeName,
      })
      .then((response) => {
        updatePermissions(response.data.data?.permissions ?? {});
        setIsLoadingChatPermissions(false);
        return response.data.data?.permissions[CHAT_PERMISSIONS.AccessChat] ?? false;
      })
      .catch(() => {
        return false;
      });
  };

  const loadCalls = () => {
    loadOrganisationsAppsAndHomeOrganisation();
    loadUserOrganisation();
    getAllPermissions();
  };

  const getChatPanelSizes = (): Map<string, number> => {
    const sizeMap = new Map<string, number>();
    if (chatExpanded) {
      sizeMap.set('minSize', 100);
      sizeMap.set('maxSize', 100);
      sizeMap.set('defaultSize', 100);
    } else {
      sizeMap.set('minSize', 25);
      sizeMap.set('maxSize', 50);
      sizeMap.set('defaultSize', 30);
    }
    return sizeMap;
  };

  const handleAppActionsNavigation = (remote: RemoteModule): void => {
    const { path, module, actionsModule, appCodeName } = remote;
    const route = `${routes.appActions(selectedOrganisation?.codeName ?? '', appCodeName)}/${APP_ACTIONS_NESTED_ROUTES.ADVANCED}/${path}`;
    navigate(route, {
      state: { module, appCodeName: appCodeName.replace('axon', ''), actionsModule },
    });
  };

  const getChatPanelSizesCallback = useCallback(getChatPanelSizes, [chatExpanded]);

  if (!!isLoading || !!isLoadingAuth || isLoadingChatPermissions) return null;

  return (
    <Fragment>
      <AutoLogout />
      <NavigationBar chatEnabled={isChatEnabled} chatOpen={chatOpen} setChatOpen={setChatOpen} />
      <PanelGroup autoSaveId="chatLayout" direction="horizontal">
        <Panel id="content">
          <Content>
            <Routes>
              <Route path={routes.signIn} element={<Navigate to={routes.home} />} />
              <Route path={routes.externalLogin()} element={<ExternalLoginPage />} />
              <Route path={routes.signOut} element={<SignOutPage />} />
              <Route path={routes.home} element={<DashboardPage />} />
              <Route path={routes.notFound} element={<NotFoundPage />} />
              <Route path={routes.forbidden} element={<ForbiddenPage />} />
              <Route path={routes.organisationDisabled} element={<OrganisationDisabledPage />} />
              <Route path={routes.apps} element={<AppsPage />} />
              <Route path={routes.organisations} element={<OrganisationsPage />} />
              <Route
                path={routes.organisation()}
                element={
                  <RouteParamSetter>
                    <OrganisationPage />
                  </RouteParamSetter>
                }
              />
              <Route path={routes.createOrganisation} element={<CreateNewOrganisationPage />} />
              <Route path={routes.createOrganisationApps} element={<CreateNewOrganisationAppsPage />} />
              <Route path={routes.editOrganisationApps()} element={<EditOrganisationAppsPage />} />
              <Route
                path={`${routes.myApp()}*`}
                element={
                  <RouteParamSetter>
                    <MyAppPage />
                  </RouteParamSetter>
                }
              />
              <Route
                path={`${routes.appRedirect()}*`}
                element={
                  <RouteParamSetter>
                    <AppRedirect />
                  </RouteParamSetter>
                }
              />
              <Route path={routes.manageApp()} element={<ManageAppPage />} />
              <Route
                path={routes.myOrganisationApps()}
                element={
                  <RouteParamSetter>
                    <MyOrganisationPage />
                  </RouteParamSetter>
                }
              />
              <Route path={routes.createApp} element={<CreateNewAppPage />} />
              <Route
                path={routes.manageOrganisation()}
                element={
                  <RouteParamSetter>
                    <ManageOrganisationPage />
                  </RouteParamSetter>
                }
              />
              <Route
                path={routes.organisationUsers()}
                element={
                  <RouteParamSetter>
                    <OrganisationUsersPage />
                  </RouteParamSetter>
                }
              />
              <Route
                path={routes.manageAxonAccess()}
                element={
                  <RouteParamSetter>
                    <ManageAxonAccessPage />
                  </RouteParamSetter>
                }
              />
              <Route
                path={routes.myOrganisation()}
                element={
                  <RouteParamSetter>
                    <RedirectToMyOrganisationApps />
                  </RouteParamSetter>
                }
              />
              <Route
                path={routes.orgAuditLog()}
                element={
                  <RouteParamSetter>
                    <OrgAuditLogPage />
                  </RouteParamSetter>
                }
              />
              <Route path={routes.appActions()} element={<OrgAppActionsPage />}>
                <Route path={APP_ACTIONS_NESTED_ROUTES.APP_SETTINGS} element={<OrgAppSettingsPage />} />
                <Route path={APP_ACTIONS_NESTED_ROUTES.MANAGE_ACCESS} element={<ManageAppAccessPage />} />
                <Route path={APP_ACTIONS_NESTED_ROUTES.AUDIT_LOG} element={<OrgAuditLogPage />} />
                <Route path={`${APP_ACTIONS_NESTED_ROUTES.ADVANCED}/:pathName`} element={<ExposedAppPage />} />
              </Route>
              <Route path="/*" element={<Navigate to={routes.notFound} />} />
            </Routes>
          </Content>
        </Panel>
        {isChatEnabled && chatOpen && (
          <>
            {!chatExpanded && <ChatResize />}
            <Panel
              id={chatExpanded ? 'chat-expanded' : 'chat'}
              order={1}
              minSize={getChatPanelSizesCallback().get('minSize')}
              maxSize={getChatPanelSizesCallback().get('maxSize')}
              defaultSize={getChatPanelSizesCallback().get('defaultSize')}
            >
              <Suspense fallback={<PhlexFormSkeleton />}>
                {!failedFetchingRemote && RemoteChat && (
                  <RemoteChat
                    toggleChat={() => setChatOpen(!chatOpen)}
                    setChatExpanded={setChatExpanded}
                    chatExpanded={chatExpanded}
                    selectedOrganisation={selectedOrganisation?.codeName ?? ''}
                    appName={appName}
                    handleAppActionsNavigation={handleAppActionsNavigation}
                  />
                )}
              </Suspense>
              {failedFetchingRemote && <PhlexInfoPanel color="red">{t('Chat.ServiceUnavailable')}</PhlexInfoPanel>}
            </Panel>
          </>
        )}
      </PanelGroup>
    </Fragment>
  );
};

const RedirectToMyOrganisationApps = () => {
  const { codeName } = useParams<{ codeName: string }>();

  return <Navigate to={routes.myOrganisationApps(codeName)} />;
};

export default App;
