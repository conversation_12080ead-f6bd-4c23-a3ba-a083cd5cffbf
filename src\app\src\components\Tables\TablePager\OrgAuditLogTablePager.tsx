import React, { useMemo, FC } from 'react';

import { PagerProps } from '@progress/kendo-react-data-tools';
import { PhlexIcon } from 'phlex-core-ui';

import components from './TablePager.styles';
import { useTranslation } from 'react-i18next';

const OrgAuditLogTablePager: FC<PagerProps> = (props: PagerProps) => {
  const { t } = useTranslation();
  const { skip, take, total, onPageChange } = props;
  const currentPage = Math.floor(skip / take) + 1;

  const handleChange = (event: any, skipValue: number) => {
    if (onPageChange) {
      onPageChange({
        target: event.target,
        skip: skipValue,
        take,
        syntheticEvent: event,
        nativeEvent: event.nativeEvent,
        targetEvent: event.targetEvent,
      });
    }
  };

  const resultsLabel = useMemo<string>(() => {
    if (total > 0) {
      return `${t('AuditLog.Showing')} ${t('AuditLog.Audits')} ${skip + 1} ${t('AuditLog.To')} ${take + skip}`;
    }

    return '';
  }, [skip, t, take, total]);

  const { ActionsBox, Row, Count } = components;

  return (
    <Row>
      <Count>{resultsLabel}</Count>
      {total > 0 && (
        <ActionsBox>
          <button disabled={skip === 0} onClick={(e) => handleChange(e, 0)} title={t('AuditLog.FirstPage')}>
            <PhlexIcon name="first_page" size="large" />
          </button>
          <button disabled={skip === 0} onClick={(e) => handleChange(e, (currentPage - 2) * take)} title={t('AuditLog.Previous')}>
            <PhlexIcon name="navigate_before" size="large" />
          </button>
          <button disabled={total === 0 || total < 20} onClick={(e) => handleChange(e, currentPage * take)} title={t('AuditLog.Next')}>
            <PhlexIcon name="navigate_next" size="large" />
          </button>
        </ActionsBox>
      )}
    </Row>
  );
};

export default OrgAuditLogTablePager;
