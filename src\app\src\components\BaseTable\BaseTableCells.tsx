// PLEASE NOTE THAT ALTHOUGH THIS FILE IS USUALLY AUTO-GENERATED, DUE TO ONGOING TEMPLATE WORK, THIS HAS BEEN UPDATED MANUALLY
/*** AUTO-GENERATED FILE - EDITS WILL BE OVERWRITTEN ***/

import React, { ReactElement } from 'react';
import { PhlexBadge } from 'phlex-core-ui';
import { GridCellProps } from '@progress/kendo-react-grid';

import styles from './BaseTable.styles';
import { t } from 'i18next';

const { CellIcon } = styles;

const toTextualDate = (date: string | null | undefined, isFullMonth: boolean = true): string => {
  if (date) {
    const newDate = new Date(date);
    return newDate
      ? `${newDate.getDate()} ${newDate.toLocaleString('default', { month: isFullMonth ? 'long' : 'short' })} ${newDate.getFullYear()}`
      : '';
  }
  return '';
};

// TODO - Make phlex-core-ui export these colors and reference here instead.
export type BadgeCellColor = 'client' | 'grey' | 'white' | 'red' | 'amber' | 'green' | 'blue';

export interface BadgeCellProps {
  title: string;
  color?: BadgeCellColor;
}

export interface IconCellProps {
  title: string;
  icon: string;
}

export const StandardCell = (cell: GridCellProps, editHandler: (dataItem: any) => void): ReactElement => {
  const text = cell.dataItem[cell.field ?? ''];
  return (
    <td title={text} onClick={() => editHandler(cell)}>
      {text}
    </td>
  );
};

export const DateTimeCell = (cell: GridCellProps, editHandler: (dataItem: any) => void): ReactElement => {
  const text = cell.dataItem[cell.field ?? ''];
  const formattedDate = text ? toTextualDate(text, false) : t('CRUDTemplate.Cells.NoDate');

  return (
    <td title={formattedDate} onClick={() => editHandler(cell)}>
      <span>{formattedDate}</span>
    </td>
  );
};

export const BadgeCell = (cell: GridCellProps, editHandler: (dataItem: any) => void, color?: BadgeCellColor): ReactElement => {
  const text = cell.dataItem[cell.field ?? ''];

  return (
    <td title={text} onClick={() => editHandler(cell)}>
      <PhlexBadge label={text.toString()} tooltip={text} size="small" statusColor={color} />
    </td>
  );
};

export const BadgeMapCell = (cell: GridCellProps, editHandler: (dataItem: any) => void, map: Map<string, BadgeCellProps>): ReactElement => {
  const key = cell.dataItem[cell.field ?? ''];
  const props = map.get(key) ?? undefined;
  const text = props?.title ?? key.toString();

  return (
    <td title={text} onClick={() => editHandler(cell)}>
      <PhlexBadge label={text} tooltip={text} size="small" statusColor={props?.color} />
    </td>
  );
};

export const TextMapCell = (cell: GridCellProps, editHandler: (dataItem: any) => void, map: Map<unknown, string>): ReactElement => {
  const key = cell.dataItem[cell.field ?? ''];
  const textFromMap = map.get(key) ?? key.toString();
  return (
    <td title={textFromMap} onClick={() => editHandler(cell)}>
      {textFromMap}
    </td>
  );
};

export const IconCell = (cell: GridCellProps, onClick: (cell: GridCellProps) => void, iconCellProps: IconCellProps) => {
  return (
    <CellIcon onClick={() => onClick(cell)} title={iconCellProps.title}>
      {iconCellProps.icon}
    </CellIcon>
  );
};

export const IconMapCell = (cell: GridCellProps, onClick: (cell: GridCellProps) => void, map: Map<unknown, IconCellProps>) => {
  const key = cell.dataItem[cell.field ?? ''];
  const iconProps = map.get(key) ?? { title: 'Unknown', icon: 'question_mark' };
  return IconCell(cell, onClick, iconProps);
};

export const DeleteCell = (cell: GridCellProps, onClick: (cell: GridCellProps) => void) =>
  IconCell(cell, onClick, { title: t('CRUDTemplate.Cells.Delete'), icon: 'delete' });
export const RemoveCell = (cell: GridCellProps, onClick: (cell: GridCellProps) => void) =>
  IconCell(cell, onClick, { title: t('CRUDTemplate.Cells.Remove'), icon: 'close' });
export const EditCell = (cell: GridCellProps, onClick: (cell: GridCellProps) => void) =>
  IconCell(cell, onClick, { title: t('CRUDTemplate.Cells.Edit'), icon: 'edit' });
