import React, { RefObject, FunctionComponent, useState, useEffect } from 'react';
import { ExcelExport, ExcelExportColumn } from '@progress/kendo-react-excel-export';
import { useTranslation } from 'react-i18next';
import { toDateTimeFormat } from 'shared/services/date-time-format';
import { DataItem, PhlexPopoutMenu } from 'phlex-core-ui';
import { toast } from 'react-toastify';
import { useParams } from 'react-router-dom';
import { SortDescriptor } from '@progress/kendo-data-query';
import { IAuditParams } from 'store/audit/types';
import { DEFAULT_EXPORT_SIZE, FILENAME, FILE_TYPE } from 'constants/export';
import { useAuditStore } from 'store';
import Axios from 'axios';

interface ExportProps {
  filters: string;
  sort: Array<SortDescriptor>;
}

const OrgAuditExcelExportComponent: FunctionComponent<ExportProps> = (props: ExportProps) => {
  const maxExportedAudits = DEFAULT_EXPORT_SIZE;
  const fileName = `${FILENAME}_${new Date().toLocaleString('en-GB')}`;
  const { t } = useTranslation();
  const _exporter = React.createRef<ExcelExport>();
  const [isLoading, setIsLoading] = useState(false);
  const { codeName, appCodeName } = useParams<{ codeName: string; appCodeName: string }>();
  const [data, setData] = useState<DataItem[]>([]);
  const [selectedFileType, setSelectedFileType] = useState<string>();

  const { auditUrl } = useAuditStore();

  const fetchData = async () => {
    if (codeName && appCodeName) {
      const orderBy = props.sort?.[0]?.field ? `${props.sort?.[0]?.field} ${props.sort?.[0]?.dir}` : '';
      const params: IAuditParams = { $top: maxExportedAudits, $skip: 0 };
      if (orderBy) {
        params.$orderby = orderBy;
      }
      if (props.filters) {
        params.$filter = props.filters;
      }

      try {
        if (auditUrl) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const result = await Axios.get<any>(auditUrl, {
            params: params,
          });
          setData(result?.data?.value as DataItem[]);
        }
      } catch {
        setIsLoading(false);
        toast.error(t('Shared.ApiFailedErrorMessage'));
      }
    }
  };

  const auditExport = async () => {
    setIsLoading(true);
    await fetchData();
  };

  useEffect(() => {
    if (isLoading && selectedFileType == FILE_TYPE.EXCEL) {
      save(_exporter);
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    if (isLoading && selectedFileType == FILE_TYPE.JSON) {
      jsonExport();
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const jsonExport = () => {
    const mappedData = mapData();
    const jsonString = `data:text/json;charset=utf-8,${encodeURIComponent(JSON.stringify(mappedData, null, 2))}`;
    const link = document.createElement('a');
    link.href = jsonString;
    link.download = `${fileName}.json`;
    // eslint-disable-next-line testing-library/no-node-access
    link.click();

    exportComplete();
  };

  const save = (component: RefObject<ExcelExport>) => {
    if (component.current) {
      const mappedData = mapData();
      const options = component.current.workbookOptions(mappedData);
      component.current.save(options);
    }
  };

  const mapData = () => {
    return data?.map((log: DataItem) => {
      return {
        StartDate: toDateTimeFormat(String(log.StartDate)),
        EventType: log.EventType,
        EventCategory: log.EventCategory,
        EventDescription: log.EventDescription,
        UserEmail: log.UserEmail ? log.UserEmail : log.UserId,
        EventOutcome: log.EventOutcome,
      };
    });
  };

  const exportComplete = () => {
    toast.success(t('AuditLog.ExportCompleted'), {
      toastId: 'export-completed',
    });
    toast.info(`${t('AuditLog.MaxExportedMessage')}${maxExportedAudits}.`, {
      toastId: 'max-exported',
    });
  };

  const onExportAuditButton = (fileType: string) => {
    if (isLoading) {
      return;
    }

    setSelectedFileType(fileType);
    auditExport();
  };

  return (
    <>
      <PhlexPopoutMenu
        buttonType="primary"
        menuPosition="below"
        label={t('AuditLog.ExportButton')}
        isLoading={isLoading}
        actions={[
          {
            icon: 'table',
            label: t('AuditLog.Excel'),
            onClick: () => onExportAuditButton(FILE_TYPE.EXCEL),
          },
          {
            icon: 'database',
            label: t('AuditLog.Json'),
            onClick: () => onExportAuditButton(FILE_TYPE.JSON),
          },
        ]}
      />
      <ExcelExport onExportComplete={exportComplete} collapsible={true} fileName={fileName} ref={_exporter}>
        <ExcelExportColumn field="StartDate" title={t('AuditLog.Date')} width={500} />
        <ExcelExportColumn field="EventType" title={t('AuditLog.EventType')} />
        <ExcelExportColumn field="EventCategory" title={t('AuditLog.EventCategory')} />
        <ExcelExportColumn field="EventDescription" title={t('AuditLog.EventDescription')} />
        <ExcelExportColumn field="UserEmail" title={t('AuditLog.User')} />
        <ExcelExportColumn field="EventOutcome" title={t('AuditLog.EventOutcome')} />
      </ExcelExport>
    </>
  );
};

export default OrgAuditExcelExportComponent;
