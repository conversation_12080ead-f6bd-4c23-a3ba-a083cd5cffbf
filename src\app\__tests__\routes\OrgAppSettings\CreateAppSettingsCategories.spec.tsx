import createAppSettingsCategories from '../../../src/routes/OrgAppSettings/createAppSettingsCategories';
import dummyData from '../../testUtils/mockOrgAppSettingsGetResponse';

describe('CreateAppSettingsCategories', () => {
  it('should return the right number of categories', () => {
    // @ts-ignore
    const categories = createAppSettingsCategories(dummyData);

    expect(categories.length).toEqual(3);
  });

  it('should return the right number of settings in each category', () => {
    // @ts-ignore
    const categories = createAppSettingsCategories(dummyData);

    expect(categories[0].settings.length).toEqual(3);
    expect(categories[1].settings.length).toEqual(2);
    expect(categories[2].settings.length).toEqual(1);
  });
});
